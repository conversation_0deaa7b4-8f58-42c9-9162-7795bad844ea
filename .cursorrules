# AgriTech Seller Backend - Cursor Rules

## Project Context
You are working on the AgriTech Seller Backend, a comprehensive microservices platform for agricultural operations. This system manages farmer-sellers, farm management, crop monitoring, and agricultural commerce using Node.js, TypeScript, MongoDB, and Elasticsearch in an Nx monorepo.

## Architecture Overview
- **Architecture**: Microservices with API Gateway (11 services total)
- **Framework**: Nx Monorepo with Node.js/Express/TypeScript
- **Databases**: MongoDB (primary), Elasticsearch (search/analytics)
- **Authentication**: JWT with role-based access control
- **Ports**: 3000 (API Gateway), 3001-3010 (individual services)

## Core Principles

### 1. Microservices Architecture
- Each service in `apps/` is independent with its own database models, controllers, and business logic
- Services communicate through API Gateway (port 3000) or direct service calls
- Shared code belongs in `libs/shared/` or `libs/feature/`
- Follow single responsibility principle for each service

### 2. Project Structure Adherence
```
apps/
├── seller-service/       # Port 3001 - Farmer registration & management
├── admin-service/        # Port 3002 - Administrative functions
├── analytics-service/    # Port 3003 - Data analytics & reporting
├── crop-service/         # Port 3004 - Crop lifecycle management
├── farm-service/         # Port 3005 - Farm registration & management
├── notification-service/ # Port 3008 - Communication & alerts
├── order-service/        # Port 3009 - Order processing
└── [other services]/

libs/
├── shared/
│   ├── database-connectors/ # MongoDB/Elasticsearch schemas & repos
│   └── utils/              # Common utilities & middleware
└── feature/
    └── auth/               # Authentication library
```

### 3. Documentation Synchronization
**CRITICAL**: When making structural changes, ALWAYS update:
- `PROJECT_STRUCTURE.md` - Reflect any new directories, files, or architectural changes
- `PROJECT_PLAN.md` - Update completion percentages, add new features, mark items as complete
- Service-specific documentation in `apidocs/services/`

## Coding Standards

### TypeScript & Node.js
- Use TypeScript exclusively with strict mode enabled
- Follow Express.js patterns for API endpoints
- Use async/await for asynchronous operations
- Implement proper error handling with try-catch blocks
- Use consistent naming: camelCase for variables/functions, PascalCase for classes/interfaces

### File Structure Convention
Each service should follow this structure:
```
src/
├── assets/           # Static assets
├── config/
│   └── db/          # Database configurations
├── controllers/     # Request handlers
├── dto/            # Data Transfer Objects
├── middleware/     # Service-specific middleware
├── models/         # Database models
├── routes/         # Route definitions
└── services/       # Business logic
```

### Database Patterns
- Use Mongoose for MongoDB operations
- All schemas in `libs/shared/database-connectors/schemas/mongo/`
- Repository pattern in `libs/shared/database-connectors/repositories/mongo/`
- Elasticsearch schemas in `libs/shared/database-connectors/schemas/elastic/`
- Always include proper indexing and validation
- Use connection pooling for performance

### API Design Standards
- RESTful endpoints with consistent naming
- Use HTTP status codes correctly (200, 201, 400, 401, 403, 404, 500)
- Standardized response format:
  ```typescript
  {
    success: boolean;
    data?: any;
    error?: {
      code: string;
      message: string;
      details?: any;
    };
  }
  ```
- Include Swagger/OpenAPI documentation for all endpoints
- Implement request validation using DTOs

### Authentication & Security
- JWT tokens for authentication
- Role-based access control (RBAC)
- Rate limiting on all endpoints
- Input validation and sanitization
- Password hashing with bcrypt
- Helmet for security headers
- CORS configuration

## Implementation Guidelines

### When Adding New Services
1. Create service in `apps/` following the established structure
2. Add port assignment in SERVICE_PORTS.md
3. Update nx.json configuration
4. Create corresponding documentation in `apidocs/services/`
5. Update PROJECT_STRUCTURE.md with new service details
6. Update DETAILED_PROJECT_PLAN.md with implementation status

### When Adding New Features
1. Check if feature belongs in existing service or needs new service
2. For shared functionality, add to `libs/shared/`
3. Follow existing patterns for controllers, services, and models
4. Add proper TypeScript types and interfaces
5. Implement comprehensive error handling
6. Add unit and integration tests
7. Update relevant documentation

### Database Operations
- Always use repository pattern from `libs/shared/database-connectors/`
- Implement proper error handling for database operations
- Use transactions for multi-document operations
- Include proper indexing strategies
- Validate data using Mongoose schemas

### Error Handling
- Use custom error classes in `libs/shared/utils/`
- Implement circuit breaker pattern for service calls
- Log errors with proper context and stack traces
- Return user-friendly error messages
- Include error tracking and monitoring

### Testing Requirements
- Unit tests for all business logic
- Integration tests for API endpoints
- Mock external dependencies
- Achieve minimum 80% code coverage
- Test error scenarios and edge cases

## Service-Specific Guidelines

### Seller Service (Port 3001)
- Manage farmer registration and profiles
- Implement KYC verification workflow
- Handle document upload and validation
- Manage seller status and verification
- Include bank account management

### Farm Service (Port 3005)
- Handle farm registration and management
- Implement location services and GPS coordination
- Manage farm certifications and infrastructure
- Support multi-plot farms
- Include farm verification workflow

### Crop Service (Port 3004)
- Manage crop lifecycle (Planting → Growing → Maturing → Ready)
- Track crop health (Healthy, Warning, Critical)
- Handle resource management (water, fertilizer, pesticides)
- Include weather integration and soil monitoring
- Implement sustainability metrics

## Code Quality

### Linting & Formatting
- Use ESLint with TypeScript rules
- Follow Prettier configuration
- Implement pre-commit hooks
- Maintain consistent code style across services

### Performance
- Use connection pooling for databases
- Implement caching where appropriate
- Optimize database queries with proper indexing
- Use compression for API responses
- Monitor and log performance metrics

### Security
- Never commit sensitive data (API keys, passwords)
- Use environment variables for configuration
- Implement proper input validation
- Follow OWASP security guidelines
- Regular security audits and dependency updates

## Documentation Requirements

### Code Documentation
- JSDoc comments for all public functions and classes
- Clear variable and function naming
- Document complex business logic
- Include usage examples in comments

### API Documentation
- Swagger/OpenAPI specs for all endpoints
- Include request/response examples
- Document authentication requirements
- Specify error codes and messages

### Project Documentation
- Keep README files updated for each service
- Document deployment procedures
- Include troubleshooting guides
- Maintain architecture decision records

## Development Workflow

### Feature Development
1. Create feature branch from main
2. Implement feature following established patterns
3. Add comprehensive tests
4. Update documentation
5. Ensure all services still work together
6. Submit PR with detailed description

### Code Review Guidelines
- Check adherence to architectural patterns
- Verify error handling implementation
- Ensure tests cover edge cases
- Validate documentation updates
- Confirm performance considerations

### Deployment Considerations
- Each service can be deployed independently
- Maintain backward compatibility for APIs
- Include health check endpoints
- Implement graceful shutdown procedures
- Monitor service dependencies

## Environment Management
- Use `.env` files for local development
- Separate configurations for dev/staging/production
- Never hardcode environment-specific values
- Document all required environment variables

## Monitoring & Logging
- Structured logging with consistent format
- Include correlation IDs for request tracking
- Monitor service health and performance
- Implement alerting for critical issues
- Log all database operations and external API calls

Remember: This is a complex microservices architecture. Always consider the impact of changes on other services and maintain consistency across the entire platform. When in doubt, follow existing patterns and consult the project documentation. 