{"name": "analytics-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/analytics-service/src", "projectType": "application", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/analytics-service", "format": ["cjs"], "bundle": true, "main": "apps/analytics-service/src/main.ts", "tsConfig": "apps/analytics-service/tsconfig.app.json", "assets": ["apps/analytics-service/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {"fileReplacements": [{"replace": "apps/analytics-service/.env", "with": "apps/analytics-service/.env.development"}]}, "production": {"fileReplacements": [{"replace": "apps/analytics-service/.env", "with": "apps/analytics-service/.env.production"}]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "analytics-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "analytics-service:build:development"}, "production": {"buildTarget": "analytics-service:build:production"}}}}, "tags": []}