# Crop Service

A microservice for managing crop data in the agricultural e-commerce platform.

## Overview

The Crop Service provides APIs for managing crop information, including:
- Creating, reading, updating, and deleting crop records
- Querying crops with various filters
- Searching crops using Elasticsearch
- Getting analytics on crop data

## Technologies Used

- Node.js with Express
- MongoDB for primary data storage
- Elasticsearch for search and advanced queries
- Swagger for API documentation

## Getting Started

### Prerequisites

- Node.js (v14+)
- MongoDB
- Elasticsearch

### Environment Setup

Create a `.env` file in the service root with the following variables:

```
# Server configuration
PORT=3005

# MongoDB configuration
MONGODB_URI=mongodb://localhost:27017/crop-service

# Elasticsearch configuration
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_USERNAME=
ELASTICSEARCH_PASSWORD=
ELASTICSEARCH_SSL_VERIFY=false
```

### Installation

1. Install dependencies:

```
npm install
```

2. Start the service:

```
npm start
```

## API Documentation

The API documentation is available via Swagger UI at `/api-docs` when the service is running.

### Key Endpoints

- `POST /api/crops` - Create a new crop
- `GET /api/crops/{cropId}` - Get a crop by ID
- `PUT /api/crops/{cropId}` - Update an existing crop
- `DELETE /api/crops/{cropId}` - Delete a crop
- `GET /api/crops` - Query crops with filters and pagination
- `GET /api/crops/search` - Search crops using Elasticsearch
- `GET /api/crops/analytics/{sellerId}` - Get crop analytics for a seller

## Data Model

The crop model captures detailed information about crops grown in farms, including:

- Basic information (type, variety, planting date, etc.)
- Growth stage and health status
- Expected and actual yields
- Resource usage
- Soil conditions
- Maintenance schedules
- Weather information

## Integration Points

The Crop Service integrates with:
- MongoDB for data persistence
- Elasticsearch for search functionality
- Other microservices via REST APIs

## License

This project is proprietary and confidential.

## Contact

For questions and support, please contact the development team. 