#!/usr/bin/env node

/**
 * Simple test script to verify crop search functionality
 * Run with: node test-search.js
 */

const http = require('http');

const BASE_URL = 'http://localhost:3004';

/**
 * Make HTTP request
 */
function makeRequest(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3004,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

/**
 * Test functions
 */
async function testHealthCheck() {
  console.log('\n=== Testing Health Check ===');
  try {
    const response = await makeRequest('/api/crops/health');
    console.log('Status Code:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Health check failed:', error.message);
  }
}

async function testSearch(query = 'tomato') {
  console.log(`\n=== Testing Search: "${query}" ===`);
  try {
    const response = await makeRequest(`/api/crops/search?query=${encodeURIComponent(query)}`);
    console.log('Status Code:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Search test failed:', error.message);
  }
}

async function testSearchWithFilters() {
  console.log('\n=== Testing Search with Filters ===');
  try {
    const response = await makeRequest('/api/crops/search?query=crop&page=1&limit=5');
    console.log('Status Code:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Search with filters test failed:', error.message);
  }
}

async function testSearchEndpoint() {
  console.log('\n=== Testing Search Test Endpoint ===');
  try {
    const response = await makeRequest('/api/crops/test-search?q=rice');
    console.log('Status Code:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Search test endpoint failed:', error.message);
  }
}

async function testGeneralQuery() {
  console.log('\n=== Testing General Query Endpoint ===');
  try {
    const response = await makeRequest('/api/crops?page=1&limit=5');
    console.log('Status Code:', response.statusCode);
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('General query test failed:', error.message);
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🌾 Crop Service Search Test Suite');
  console.log('==================================');
  
  // Test service health
  await testHealthCheck();
  
  // Test search functionality
  await testSearch('tomato');
  await testSearch('rice');
  await testSearch('wheat');
  
  // Test search with filters
  await testSearchWithFilters();
  
  // Test search test endpoint
  await testSearchEndpoint();
  
  // Test general query
  await testGeneralQuery();
  
  console.log('\n✅ Test suite completed!');
  console.log('\nIf you see errors, check:');
  console.log('1. Crop service is running on port 3004');
  console.log('2. Elasticsearch connection is working');
  console.log('3. MongoDB connection is working');
  console.log('4. There is data in the database');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testHealthCheck, testSearch };
