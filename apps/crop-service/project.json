{"name": "crop-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/crop-service/src", "projectType": "application", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/crop-service", "format": ["cjs"], "bundle": true, "main": "apps/crop-service/src/main.ts", "tsConfig": "apps/crop-service/tsconfig.app.json", "assets": ["apps/crop-service/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {"fileReplacements": [{"replace": "apps/crop-service/.env", "with": "apps/crop-service/.env.development"}]}, "production": {"fileReplacements": [{"replace": "apps/crop-service/.env", "with": "apps/crop-service/.env.production"}]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "crop-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "crop-service:build:development"}, "production": {"buildTarget": "crop-service:build:production"}}}}, "tags": []}