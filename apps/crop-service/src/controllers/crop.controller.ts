import { Request, Response } from 'express';
import { CropService } from '../services/crop.service';
import { 
  CreateCropDto, 
  UpdateCropDto, 
  QueryCropsDto, 
  CropAnalyticsDto 
} from '../dto/crop.dto';

/**
 * Controller for crop operations
 */
export class CropController {
  private cropService: CropService;

  /**
   * Constructor for CropController
   * @param cropService - The crop service instance
   */
  constructor(cropService: CropService) {
    this.cropService = cropService;
  }

  /**
   * Create a new crop
   * @param req - Express request
   * @param res - Express response
   */
  async createCrop(req: Request, res: Response): Promise<void> {
    try {
      console.log('Entered CropController createCrop');
      const createCropDto = req.body as CreateCropDto;
      const crop = await this.cropService.createCrop(createCropDto);
      
      res.status(201).json({
        success: true,
        data: crop
      });
    } catch (error: any) {
      console.error('Error creating crop:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to create crop',
        error: error.message
      });
    }
  }

  /**
   * Update an existing crop
   * @param req - Express request
   * @param res - Express response
   */
  async updateCrop(req: Request, res: Response): Promise<void> {
    try {
      const { cropId } = req.params;
      const updateCropDto = req.body as UpdateCropDto;
      
      const updatedCrop = await this.cropService.updateCrop(cropId, updateCropDto);
      
      if (!updatedCrop) {
        res.status(404).json({
          success: false,
          message: `Crop with ID ${cropId} not found`
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        data: updatedCrop
      });
    } catch (error: any) {
      console.error('Error updating crop:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to update crop',
        error: error.message
      });
    }
  }

  /**
   * Delete a crop
   * @param req - Express request
   * @param res - Express response
   */
  async deleteCrop(req: Request, res: Response): Promise<void> {
    try {
      const { cropId } = req.params;
      const deleted = await this.cropService.deleteCrop(cropId);
      
      if (!deleted) {
        res.status(404).json({
          success: false,
          message: `Crop with ID ${cropId} not found`
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        message: `Crop with ID ${cropId} deleted successfully`
      });
    } catch (error: any) {
      console.error('Error deleting crop:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to delete crop',
        error: error.message
      });
    }
  }

  /**
   * Get a crop by ID
   * @param req - Express request
   * @param res - Express response
   */
  async getCropById(req: Request, res: Response): Promise<void> {
    try {
      const { cropId } = req.params;
      const crop = await this.cropService.getCropById(cropId);
      
      if (!crop) {
        res.status(404).json({
          success: false,
          message: `Crop with ID ${cropId} not found`
        });
        return;
      }
      
      res.status(200).json({
        success: true,
        data: crop
      });
    } catch (error: any) {
      console.error('Error getting crop:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to get crop',
        error: error.message
      });
    }
  }

  /**
   * Query crops with filters
   * @param req - Express request
   * @param res - Express response
   */
  async queryCrops(req: Request, res: Response): Promise<void> {
    try {
      // Extract query parameters
      const queryDto: QueryCropsDto = {
        farmerId: req.query.farmerId as string,
        farmId: req.query.farmId as string,
        type: req.query.type as string,
        growthStage: req.query.growthStage as any,
        healthStatus: req.query.healthStatus as any,
        harvestDateFrom: req.query.harvestDateFrom ? new Date(req.query.harvestDateFrom as string) : undefined,
        harvestDateTo: req.query.harvestDateTo ? new Date(req.query.harvestDateTo as string) : undefined,
        page: req.query.page ? parseInt(req.query.page as string, 10) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string, 10) : 10,
        sortBy: req.query.sortBy as string,
        sortOrder: req.query.sortOrder as 'asc' | 'desc'
      };
      
      const { crops, total } = await this.cropService.queryCrops(queryDto);
      
      res.status(200).json({
        success: true,
        data: crops,
        pagination: {
          page: queryDto.page || 1,
          limit: queryDto.limit || 10,
          total
        }
      });
    } catch (error: any) {
      console.error('Error querying crops:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to query crops',
        error: error.message
      });
    }
  }

  /**
   * Search crops using Elasticsearch
   * @param req - Express request
   * @param res - Express response
   */
  async searchCrops(req: Request, res: Response): Promise<void> {
    try {
      const { query } = req.query;
      
      if (!query) {
        res.status(400).json({
          success: false,
          message: 'Search query is required'
        });
        return;
      }
      
      // Extract filters and pagination
      const filters = {
        farmerId: req.query.farmerId,
        farmId: req.query.farmId,
        growthStage: req.query.growthStage,
        healthStatus: req.query.healthStatus,
        harvestDateFrom: req.query.harvestDateFrom,
        harvestDateTo: req.query.harvestDateTo
      };
      
      const page = req.query.page ? parseInt(req.query.page as string, 10) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
      
      const { crops, total } = await this.cropService.searchCrops(query as string, filters, page, limit);
      
      res.status(200).json({
        success: true,
        data: crops,
        pagination: {
          page,
          limit,
          total
        }
      });
    } catch (error: any) {
      console.error('Error searching crops:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to search crops',
        error: error.message
      });
    }
  }

  /**
   * Get crop analytics
   * @param req - Express request
   * @param res - Express response
   */
  async getCropAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const { farmerId } = req.params;

      if (!farmerId) {
        res.status(400).json({
          success: false,
          message: 'Farmer ID is required'
        });
        return;
      }

      // Extract analytics parameters
      const analyticsDto: CropAnalyticsDto = {
        farmerId,
        farmId: req.query.farmId as string,
        fromDate: req.query.fromDate ? new Date(req.query.fromDate as string) : undefined,
        toDate: req.query.toDate ? new Date(req.query.toDate as string) : undefined
      };
      
      const analytics = await this.cropService.getCropAnalytics(analyticsDto);
      
      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error: any) {
      console.error('Error getting crop analytics:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to get crop analytics',
        error: error.message
      });
    }
  }

  /**
   * Reindex all crops
   * @param req - Express request
   * @param res - Express response
   */
  async reindexAllCrops(req: Request, res: Response): Promise<void> {
    try {
      await this.cropService.reindexAllCrops();
      
      res.status(200).json({
        success: true,
        message: 'All crops reindexed successfully'
      });
    } catch (error: any) {
      console.error('Error reindexing crops:', error);
      
      res.status(500).json({
        success: false,
        message: 'Failed to reindex crops',
        error: error.message
      });
    }
  }

  /**
   * Update crop growth stage
   * @param req - Express request
   * @param res - Express response
   */
  async updateCropGrowthStage(req: Request, res: Response): Promise<void> {
    try {
      const { cropId } = req.params;
      const { growthStage, notes } = req.body;

      const updatedCrop = await this.cropService.updateCropGrowthStage(cropId, growthStage, notes);

      if (!updatedCrop) {
        res.status(404).json({
          success: false,
          message: 'Crop not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: updatedCrop,
        message: 'Crop growth stage updated successfully'
      });
    } catch (error: any) {
      console.error('Error updating crop growth stage:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to update crop growth stage',
        error: error.message
      });
    }
  }

  /**
   * Update crop health status
   * @param req - Express request
   * @param res - Express response
   */
  async updateCropHealth(req: Request, res: Response): Promise<void> {
    try {
      const { cropId } = req.params;
      const { healthStatus, issues, notes } = req.body;

      const updatedCrop = await this.cropService.updateCropHealth(cropId, healthStatus, issues, notes);

      if (!updatedCrop) {
        res.status(404).json({
          success: false,
          message: 'Crop not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: updatedCrop,
        message: 'Crop health status updated successfully'
      });
    } catch (error: any) {
      console.error('Error updating crop health:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to update crop health',
        error: error.message
      });
    }
  }

  /**
   * Record crop harvest
   * @param req - Express request
   * @param res - Express response
   */
  async recordHarvest(req: Request, res: Response): Promise<void> {
    try {
      const { cropId } = req.params;
      const { actualYield, harvestDate, quality } = req.body;

      const updatedCrop = await this.cropService.recordHarvest(
        cropId,
        actualYield,
        harvestDate ? new Date(harvestDate) : new Date(),
        quality
      );

      if (!updatedCrop) {
        res.status(404).json({
          success: false,
          message: 'Crop not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: updatedCrop,
        message: 'Harvest recorded successfully'
      });
    } catch (error: any) {
      console.error('Error recording harvest:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to record harvest',
        error: error.message
      });
    }
  }

  /**
   * Add maintenance activity
   * @param req - Express request
   * @param res - Express response
   */
  async addMaintenanceActivity(req: Request, res: Response): Promise<void> {
    try {
      const { cropId } = req.params;
      const activity = req.body;

      const updatedCrop = await this.cropService.addMaintenanceActivity(cropId, activity);

      if (!updatedCrop) {
        res.status(404).json({
          success: false,
          message: 'Crop not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: updatedCrop,
        message: 'Maintenance activity added successfully'
      });
    } catch (error: any) {
      console.error('Error adding maintenance activity:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to add maintenance activity',
        error: error.message
      });
    }
  }

  /**
   * Test search functionality
   * @param req - Express request
   * @param res - Express response
   */
  async testSearch(req: Request, res: Response): Promise<void> {
    try {
      const testQuery = req.query.q as string || 'tomato';

      console.log(`Testing search with query: "${testQuery}"`);

      const { crops, total } = await this.cropService.searchCrops(testQuery, {}, 1, 5);

      res.status(200).json({
        success: true,
        message: 'Search test completed',
        data: {
          query: testQuery,
          results: crops,
          total,
          searchMethod: crops.length > 0 ? 'Elasticsearch or MongoDB' : 'No results found'
        }
      });
    } catch (error: any) {
      console.error('Error testing search:', error);

      res.status(500).json({
        success: false,
        message: 'Search test failed',
        error: error.message
      });
    }
  }

  /**
   * Get service health including Elasticsearch status
   * @param _req - Express request (unused)
   * @param res - Express response
   */
  async getHealthStatus(_req: Request, res: Response): Promise<void> {
    try {
      const healthStatus = await this.cropService.getHealthStatus();

      res.status(200).json({
        success: true,
        data: healthStatus
      });
    } catch (error: any) {
      console.error('Error getting health status:', error);

      res.status(500).json({
        success: false,
        message: 'Failed to get health status',
        error: error.message
      });
    }
  }
}