import express from 'express';
import * as path from 'path';
import mongoose from 'mongoose';
import cors from 'cors';
import helmet from 'helmet';
import { json } from 'body-parser';
import { initializeDbConnections } from './config/db';
import { setupCropRoutes } from './routes/crop.routes';

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(helmet());
app.use(json({ limit: '10mb' }));
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Initialize database connections and routes
const initializeApp = async () => {
  try {
    // Initialize database connections
    const esClient = await initializeDbConnections();
    
    // Set up API routes
    app.use('/api/crops', setupCropRoutes(esClient));
    
    app.get('/api', (req, res) => {
      res.send({ message: 'Welcome to crop-service!' });
    });
    
    app.get('/api/health', (req, res) => {
      res.status(200).json({
        status: 'UP',
        service: 'crop-service',
        database: mongoose.connection ? (mongoose.connection.readyState === 1 ? 'connected' : 'disconnected') : 'not applicable',
        timestamp: new Date().toISOString()
      });
    });
    
    // Error handling middleware
    app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      console.error('Unhandled error:', err);
      
      res.status(err.status || 500).json({
        status: 'error',
        message: err.message || 'Internal Server Error',
        timestamp: new Date().toISOString(),
        path: req.path
      });
    });
    
    // Start the server
    const port = process.env.PORT || 3333;
    const server = app.listen(port, () => {
      console.log(`Crop Service listening at http://localhost:${port}/api`);
    });
    
    server.on('error', console.error);
    
    // Handle graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM signal received: closing HTTP server');
      server.close(() => {
        console.log('HTTP server closed');
        // Close database connections
        mongoose.connection.close();
        esClient.close();
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('Application initialization failed:', error);
    process.exit(1);
  }
};

initializeApp();
