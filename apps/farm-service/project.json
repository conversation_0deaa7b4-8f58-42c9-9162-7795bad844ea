{"name": "farm-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/farm-service/src", "projectType": "application", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/farm-service", "format": ["cjs"], "bundle": true, "main": "apps/farm-service/src/main.ts", "tsConfig": "apps/farm-service/tsconfig.app.json", "assets": ["apps/farm-service/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {"fileReplacements": [{"replace": "apps/farm-service/.env", "with": "apps/farm-service/.env.development"}]}, "production": {"fileReplacements": [{"replace": "apps/farm-service/.env", "with": "apps/farm-service/.env.production"}]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "farm-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "farm-service:build:development"}, "production": {"buildTarget": "farm-service:build:production"}}}}, "tags": []}