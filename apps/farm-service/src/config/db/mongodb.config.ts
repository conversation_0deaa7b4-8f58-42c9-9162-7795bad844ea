import { MongoDBConnector } from '@libs/shared';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get MongoDB connection URI from environment variables or use default
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/agritech';

// Create and export MongoDB connector instance
export const mongoConnector = MongoDBConnector.getInstance({ uri: MONGODB_URI });

// Initialize connection
export const initializeDatabase = async (): Promise<void> => {
  try {
    await mongoConnector.connect();
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
}; 