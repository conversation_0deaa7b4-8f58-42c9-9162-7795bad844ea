import express from 'express';
import * as path from 'path';
import mongoose from 'mongoose';
import cors from 'cors';
import helmet from 'helmet';
import { json } from 'body-parser';
import { setupFarmRoutes } from './routes/farm.routes';
import { ElasticSearchConnector } from '@libs/shared';
import dotenv from 'dotenv';

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production'
  ? '.env.production'
  : process.env.NODE_ENV === 'development'
    ? '.env.development'
    : '.env.local';

dotenv.config({ path: path.join(__dirname, '..', envFile) });

console.log(`Loading environment from: ${envFile}`);
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`ELASTICSEARCH_NODE: ${process.env.ELASTICSEARCH_NODE}`);
console.log(`MONGODB_URI: ${process.env.MONGODB_URI ? 'SET' : 'NOT SET'}`);
console.log(`MONGODB_URI: ${process.env.MONGODB_URI ? 'SET' : 'NOT SET'}`);

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(helmet());
app.use(json({ limit: '10mb' }));
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Initialize database connections and routes
const initializeApp = async () => {
  try {
    // Connect to MongoDB
    const MONGODB_URI = process.env.MONGODB_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/agritech';

    await mongoose.connect(MONGODB_URI, {
      maxPoolSize: parseInt(process.env.MONGODB_POOL_SIZE || '10'),
      serverSelectionTimeoutMS: parseInt(process.env.MONGODB_CONNECTION_TIMEOUT || '30000'),
      socketTimeoutMS: 45000,
    });

    console.log('MongoDB connected successfully to:', MONGODB_URI.replace(/\/\/.*@/, '//***:***@'));

    // Add MongoDB event listeners
    mongoose.connection.on('error', (error) => {
      console.error('MongoDB connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      console.warn('MongoDB disconnected');
    });

    mongoose.connection.on('reconnected', () => {
      console.log('MongoDB reconnected');
    });

    // Initialize Elasticsearch connections
    const esConfig: any = {
      node: process.env.ELASTICSEARCH_NODE || 'http://localhost:9200',
      index: process.env.ELASTICSEARCH_INDEX || 'befarma',
      sslVerify: process.env.ELASTICSEARCH_SSL_VERIFY !== 'false',
    };

    // Priority 1: API Key authentication (for Elastic Cloud)
    if (process.env.ELASTICSEARCH_API_KEY) {
      esConfig.auth = {
        apiKey: process.env.ELASTICSEARCH_API_KEY,
      };

      // Add serverless mode for Elastic Cloud
      if (process.env.ELASTICSEARCH_SERVERLESS === 'true') {
        esConfig.serverMode = 'serverless';
      }
    }
    // Priority 2: Username/Password authentication (fallback)
    else if (process.env.ELASTICSEARCH_USERNAME && process.env.ELASTICSEARCH_PASSWORD) {
      esConfig.auth = {
        username: process.env.ELASTICSEARCH_USERNAME,
        password: process.env.ELASTICSEARCH_PASSWORD,
      };
    }

    // Try to initialize Elasticsearch, but don't fail if it's not available
    let esClient = null;
    try {
      const esConnector = ElasticSearchConnector.getInstance(esConfig);
      esClient = await esConnector.connect();
      console.log('Elasticsearch connected successfully');
    } catch (error) {
      console.warn('Elasticsearch connection failed, continuing with MongoDB only:', error.message);
      // Create a mock client that logs warnings when used
      esClient = {
        search: () => { throw new Error('Elasticsearch not available'); },
        index: () => { throw new Error('Elasticsearch not available'); },
        update: () => { throw new Error('Elasticsearch not available'); },
        delete: () => { throw new Error('Elasticsearch not available'); },
        close: () => { console.log('Mock Elasticsearch client closed'); }
      };
    }

    // Set up API routes
    app.use('/api/farms',  setupFarmRoutes(esClient));
    
    app.get('/api', (req, res) => {
      res.send({ message: 'Welcome to farm-service!' });
    });
    
    app.get('/api/health', (req, res) => {
      res.status(200).json({
        status: 'UP',
        service: 'farm-service',
        database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
        timestamp: new Date().toISOString()
      });
    });
    
    // Error handling middleware
    app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      console.error('Unhandled error:', err);
      
      res.status(err.status || 500).json({
        status: 'error',
        message: err.message || 'Internal Server Error',
        timestamp: new Date().toISOString(),
        path: req.path
      });
    });
    
    // Start the server
    const port = process.env.PORT || 3004;
    const server = app.listen(port, () => {
      console.log(`Farm Service listening at http://localhost:${port}/api`);
    });
    
    server.on('error', console.error);
    
    // Handle graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM signal received: closing HTTP server');
      server.close(() => {
        console.log('HTTP server closed');
        // Close database connections
        mongoose.connection.close();
        esClient.close();
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('Application initialization failed:', error);

    // Close any existing connections before exiting
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }

    process.exit(1);
  }
};

initializeApp();
