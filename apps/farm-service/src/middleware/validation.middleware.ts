import { Request, Response, NextFunction } from 'express';
import httpStatus from 'http-status';

/**
 * Middleware to validate required fields in request body
 * @param requiredFields Array of required field names
 */
export const validateRequiredFields = (requiredFields: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const missingFields = requiredFields.filter(field => {
      const value = field.split('.').reduce((obj, key) => {
        return obj && obj[key] !== undefined ? obj[key] : undefined;
      }, req.body);
      
      return value === undefined;
    });
    
    if (missingFields.length > 0) {
      return res.status(httpStatus.BAD_REQUEST).json({
        success: false,
        message: 'Missing required fields',
        missingFields,
      });
    }
    
    next();
  };
};

/**
 * Middleware to validate farm data
 */
export const validateFarmData = (req: Request, res: Response, next: NextFunction) => {
  const { totalArea } = req.body;
  
  // Validate totalArea is a positive number
  if (totalArea !== undefined && (isNaN(Number(totalArea)) || Number(totalArea) <= 0)) {
    return res.status(httpStatus.BAD_REQUEST).json({
      success: false,
      message: 'Total area must be a positive number',
    });
  }
  
  next();
}; 