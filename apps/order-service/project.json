{"name": "order-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/order-service/src", "projectType": "application", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/order-service", "format": ["cjs"], "bundle": true, "main": "apps/order-service/src/main.ts", "tsConfig": "apps/order-service/tsconfig.app.json", "assets": ["apps/order-service/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {"fileReplacements": [{"replace": "apps/order-service/.env", "with": "apps/order-service/.env.development"}]}, "production": {"fileReplacements": [{"replace": "apps/order-service/.env", "with": "apps/order-service/.env.production"}]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "order-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "order-service:build:development"}, "production": {"buildTarget": "order-service:build:production"}}}}, "tags": []}