# Order Service

## Overview
The Order Service handles all order-related operations in the AgriTech platform, including:
- Order creation and management
- Order status tracking
- Payment status management
- Order statistics and analytics
- Order cancellation and refunds

## API Endpoints

### Order Management
- `POST /api/orders` - Create a new order
- `GET /api/orders` - Get orders with filtering and pagination
- `GET /api/orders/:orderId` - Get order by ID
- `GET /api/orders/number/:orderNumber` - Get order by order number
- `PUT /api/orders/:orderId` - Update order
- `PATCH /api/orders/:orderId/status` - Update order status
- `PATCH /api/orders/:orderId/cancel` - Cancel order

### Order Statistics
- `GET /api/orders/stats` - Get order statistics

### Admin Routes
- `GET /api/orders/admin/all` - Get all orders (admin only)
- `GET /api/orders/admin/stats` - Get comprehensive order statistics (admin only)
- `PUT /api/orders/admin/:orderId` - Update any order (admin only)

## Order Status Flow

### Order Status
1. `pending` - Order created, awaiting confirmation
2. `confirmed` - Order confirmed by seller
3. `processing` - Order being prepared
4. `shipped` - Order shipped to buyer
5. `delivered` - Order delivered to buyer
6. `cancelled` - Order cancelled
7. `refunded` - Order refunded

### Payment Status
1. `pending` - Payment not yet processed
2. `paid` - Payment successful
3. `failed` - Payment failed
4. `refunded` - Payment refunded

## Data Models

### Order Model
```typescript
{
  orderNumber: string;        // Auto-generated unique order number
  buyerId: ObjectId;         // Reference to buyer
  sellerId: ObjectId;        // Reference to seller
  farmId?: ObjectId;         // Optional reference to farm
  items: OrderItem[];        // Array of order items
  subtotal: number;          // Sum of all item prices
  shippingCost: number;      // Shipping cost
  tax: number;               // Tax amount
  totalAmount: number;       // Total order amount
  currency: string;          // Currency (default: INR)
  orderStatus: OrderStatus;  // Current order status
  paymentStatus: PaymentStatus; // Current payment status
  paymentMethod?: string;    // Payment method used
  paymentTransactionId?: string; // Payment transaction ID
  shippingAddress: ShippingAddress; // Shipping address
  billingAddress?: ShippingAddress; // Optional billing address
  orderDate: Date;           // Order creation date
  expectedDeliveryDate?: Date; // Expected delivery date
  actualDeliveryDate?: Date; // Actual delivery date
  notes?: string;            // Order notes
  trackingNumber?: string;   // Shipping tracking number
  createdAt: Date;
  updatedAt: Date;
}
```

### Order Item
```typescript
{
  cropId: ObjectId;          // Reference to crop
  cropName: string;          // Crop name
  quantity: number;          // Quantity ordered
  unitPrice: number;         // Price per unit
  totalPrice: number;        // Total price for this item
  unit: string;              // Unit of measurement (kg, tons, etc.)
}
```

### Shipping Address
```typescript
{
  fullName: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phoneNumber: string;
}
```

## Environment Variables

### Application Configuration
- `NODE_ENV` - Environment (development/production)
- `PORT` - Service port (default: 3009)
- `SERVICE_NAME` - Service name

### Database Configuration
- `MONGODB_URI` - MongoDB connection URI
- `MONGODB_URI` - Alternative MongoDB URI
- `MONGODB_CONNECTION_TIMEOUT` - Connection timeout
- `MONGODB_POOL_SIZE` - Connection pool size

### Elasticsearch Configuration
- `ELASTICSEARCH_NODE` - Elasticsearch node URL
- `ELASTICSEARCH_INDEX` - Elasticsearch index name
- `ELASTICSEARCH_API_KEY` - API key for authentication
- `ELASTICSEARCH_SERVERLESS` - Enable serverless mode

### JWT Configuration
- `JWT_SECRET` - JWT secret key
- `JWT_EXPIRES_IN` - JWT expiration time

### CORS Configuration
- `ALLOWED_ORIGINS` - Allowed CORS origins

## Authentication

The service uses JWT (JSON Web Tokens) for authentication. To access protected endpoints:

1. Include the token in the Authorization header:
   ```
   Authorization: Bearer <your-token>
   ```

2. The token should contain user information including:
   - `id` - User ID
   - `email` - User email
   - `role` - User role (buyer, seller, admin)
   - `sellerId` - Seller ID (for sellers)
   - `buyerId` - Buyer ID (for buyers)

## Rate Limiting

The service implements different rate limits for different operations:

- **Standard operations**: 100 requests per 15 minutes
- **Order creation**: 10 requests per 5 minutes
- **Order updates**: 50 requests per 10 minutes
- **Read operations**: 200 requests per 15 minutes

## Error Handling

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation successful",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": [ ... ]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Project Structure
```
src/
├── controllers/           # API controllers
├── services/             # Business logic services
├── models/               # Database models
├── dto/                  # Data transfer objects
├── routes/               # API routes
├── middleware/           # Middleware functions
├── config/               # Configuration files
└── main.ts              # Application entry point
```

## Development

### Running the Service
```bash
# Development mode
npm run serve:order

# Or using nx directly
npx nx serve order-service
```

### Building the Service
```bash
npx nx build order-service
```

### Testing
```bash
# Run tests (when implemented)
npx nx test order-service
```

## Dependencies

### Core Dependencies
- `express` - Web framework
- `mongoose` - MongoDB ODM
- `cors` - CORS middleware
- `helmet` - Security middleware
- `body-parser` - Request body parsing
- `jsonwebtoken` - JWT authentication
- `express-rate-limit` - Rate limiting
- `ajv` - JSON schema validation
- `dotenv` - Environment variables

### Shared Libraries
- `@libs/shared` - Shared utilities and connectors

## Health Check

The service provides a health check endpoint at `/api/health` that returns:
- Service status
- Database connection status
- Elasticsearch connection status
- Timestamp

## Logging

The service logs important events including:
- Database connections
- Order operations
- Authentication events
- Errors and warnings

## Security Features

- JWT-based authentication
- Role-based access control
- Rate limiting
- Input validation
- CORS protection
- Security headers (helmet)
- Request body size limits
