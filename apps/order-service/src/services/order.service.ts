import { Order, IOrder, OrderStatus, PaymentStatus } from '../models/order.model';
import { CreateOrderDto, UpdateOrderDto, OrderQueryDto, OrderResponseDto, OrderStatsDto } from '../dto/order.dto';
import mongoose from 'mongoose';

export class OrderService {
  private esClient: any;

  constructor(esClient?: any) {
    this.esClient = esClient;
  }

  /**
   * Create a new order
   */
  async createOrder(orderData: CreateOrderDto): Promise<OrderResponseDto> {
    try {
      // Calculate item totals
      const items = orderData.items.map(item => ({
        ...item,
        totalPrice: item.quantity * item.unitPrice
      }));

      // Create order with calculated totals
      const order = new Order({
        ...orderData,
        items,
        shippingCost: orderData.shippingCost || 0,
        tax: orderData.tax || 0,
        currency: orderData.currency || 'INR'
      });

      const savedOrder = await order.save();

      // Try to index in Elasticsearch
      if (this.esClient) {
        try {
          await this.indexOrderInElasticsearch(savedOrder);
        } catch (esError) {
          console.warn('Failed to index order in Elasticsearch:', esError.message);
        }
      }

      return this.transformOrderToDto(savedOrder);
    } catch (error) {
      console.error('Error creating order:', error);
      throw new Error(`Failed to create order: ${error.message}`);
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(orderId: string): Promise<OrderResponseDto | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(orderId)) {
        throw new Error('Invalid order ID format');
      }

      const order = await (Order as any).findById(orderId)
        .populate('buyerId', 'name email')
        .populate('farmerId', 'businessName email')
        .populate('farmId', 'name location')
        .lean();

      if (!order) {
        return null;
      }

      return this.transformOrderToDto(order);
    } catch (error) {
      console.error('Error fetching order:', error);
      throw new Error(`Failed to fetch order: ${error.message}`);
    }
  }

  /**
   * Get order by order number
   */
  async getOrderByNumber(orderNumber: string): Promise<OrderResponseDto | null> {
    try {
      const order = await (Order as any).findOne({ orderNumber })
        .populate('buyerId', 'name email')
        .populate('farmerId', 'businessName email')
        .populate('farmId', 'name location')
        .lean();

      if (!order) {
        return null;
      }

      return this.transformOrderToDto(order);
    } catch (error) {
      console.error('Error fetching order by number:', error);
      throw new Error(`Failed to fetch order: ${error.message}`);
    }
  }

  /**
   * Update order
   */
  async updateOrder(orderId: string, updateData: UpdateOrderDto): Promise<OrderResponseDto | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(orderId)) {
        throw new Error('Invalid order ID format');
      }

      const updatedOrder = await (Order as any).findByIdAndUpdate(
        orderId,
        { $set: updateData },
        { new: true, runValidators: true }
      )
        .populate('buyerId', 'name email')
        .populate('farmerId', 'businessName email')
        .populate('farmId', 'name location')
        .lean();

      if (!updatedOrder) {
        return null;
      }

      // Try to update in Elasticsearch
      if (this.esClient) {
        try {
          await this.updateOrderInElasticsearch(updatedOrder);
        } catch (esError) {
          console.warn('Failed to update order in Elasticsearch:', esError.message);
        }
      }

      return this.transformOrderToDto(updatedOrder);
    } catch (error) {
      console.error('Error updating order:', error);
      throw new Error(`Failed to update order: ${error.message}`);
    }
  }

  /**
   * Get orders with filtering and pagination
   */
  async getOrders(query: OrderQueryDto): Promise<{
    orders: OrderResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const {
        buyerId,
        farmerId,
        farmId,
        orderStatus,
        paymentStatus,
        startDate,
        endDate,
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = query;

      // Build filter query
      const filter: any = {};

      if (buyerId && mongoose.Types.ObjectId.isValid(buyerId)) {
        filter.buyerId = buyerId;
      }

      if (farmerId && mongoose.Types.ObjectId.isValid(farmerId)) {
        filter.farmerId = farmerId;
      }

      if (farmId && mongoose.Types.ObjectId.isValid(farmId)) {
        filter.farmId = farmId;
      }

      if (orderStatus) {
        filter.orderStatus = orderStatus;
      }

      if (paymentStatus) {
        filter.paymentStatus = paymentStatus;
      }

      if (startDate || endDate) {
        filter.orderDate = {};
        if (startDate) {
          filter.orderDate.$gte = new Date(startDate);
        }
        if (endDate) {
          filter.orderDate.$lte = new Date(endDate);
        }
      }

      // Calculate pagination
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute queries
      const [orders, total] = await Promise.all([
        (Order as any).find(filter)
          .populate('buyerId', 'name email')
          .populate('farmerId', 'businessName email')
          .populate('farmId', 'name location')
          .sort(sortOptions)
          .skip(skip)
          .limit(limit)
          .lean(),
        (Order as any).countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        orders: orders.map(order => this.transformOrderToDto(order)),
        total,
        page,
        limit,
        totalPages
      };
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw new Error(`Failed to fetch orders: ${error.message}`);
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStats(farmerId?: string): Promise<OrderStatsDto> {
    try {
      const filter: any = {};
      if (farmerId && mongoose.Types.ObjectId.isValid(farmerId)) {
        filter.farmerId = farmerId;
      }

      const [
        totalOrders,
        revenueResult,
        statusStats,
        paymentStats,
        recentOrders
      ] = await Promise.all([
        (Order as any).countDocuments(filter),
        (Order as any).aggregate([
          { $match: filter },
          {
            $group: {
              _id: null,
              totalRevenue: { $sum: '$totalAmount' },
              averageOrderValue: { $avg: '$totalAmount' }
            }
          }
        ]),
        (Order as any).aggregate([
          { $match: filter },
          {
            $group: {
              _id: '$orderStatus',
              count: { $sum: 1 }
            }
          }
        ]),
        (Order as any).aggregate([
          { $match: filter },
          {
            $group: {
              _id: '$paymentStatus',
              count: { $sum: 1 }
            }
          }
        ]),
        (Order as any).find(filter)
          .populate('buyerId', 'name email')
          .populate('farmerId', 'businessName email')
          .sort({ createdAt: -1 })
          .limit(5)
          .lean()
      ]);

      // Process results
      const totalRevenue = revenueResult[0]?.totalRevenue || 0;
      const averageOrderValue = revenueResult[0]?.averageOrderValue || 0;

      const ordersByStatus: any = {};
      Object.values(OrderStatus).forEach(status => {
        ordersByStatus[status] = 0;
      });
      statusStats.forEach((stat: any) => {
        ordersByStatus[stat._id] = stat.count;
      });

      const ordersByPaymentStatus: any = {};
      Object.values(PaymentStatus).forEach(status => {
        ordersByPaymentStatus[status] = 0;
      });
      paymentStats.forEach((stat: any) => {
        ordersByPaymentStatus[stat._id] = stat.count;
      });

      return {
        totalOrders,
        totalRevenue,
        averageOrderValue,
        ordersByStatus,
        ordersByPaymentStatus,
        recentOrders: recentOrders.map(order => this.transformOrderToDto(order))
      };
    } catch (error) {
      console.error('Error fetching order stats:', error);
      throw new Error(`Failed to fetch order statistics: ${error.message}`);
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(orderId: string, reason?: string): Promise<OrderResponseDto | null> {
    try {
      const updateData: UpdateOrderDto = {
        orderStatus: OrderStatus.CANCELLED,
        notes: reason ? `Cancelled: ${reason}` : 'Order cancelled'
      };

      return await this.updateOrder(orderId, updateData);
    } catch (error) {
      console.error('Error cancelling order:', error);
      throw new Error(`Failed to cancel order: ${error.message}`);
    }
  }

  /**
   * Transform order document to DTO
   */
  private transformOrderToDto(order: any): OrderResponseDto {
    return {
      id: order._id.toString(),
      orderNumber: order.orderNumber,
      buyerId: order.buyerId._id ? order.buyerId._id.toString() : order.buyerId.toString(),
      farmerId: order.farmerId._id ? order.farmerId._id.toString() : order.farmerId.toString(),
      farmId: order.farmId ? (order.farmId._id ? order.farmId._id.toString() : order.farmId.toString()) : undefined,
      items: order.items,
      subtotal: order.subtotal,
      shippingCost: order.shippingCost,
      tax: order.tax,
      totalAmount: order.totalAmount,
      currency: order.currency,
      orderStatus: order.orderStatus,
      paymentStatus: order.paymentStatus,
      paymentMethod: order.paymentMethod,
      paymentTransactionId: order.paymentTransactionId,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      orderDate: order.orderDate,
      expectedDeliveryDate: order.expectedDeliveryDate,
      actualDeliveryDate: order.actualDeliveryDate,
      notes: order.notes,
      trackingNumber: order.trackingNumber,
      orderAgeInDays: order.orderAgeInDays,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    };
  }

  /**
   * Index order in Elasticsearch
   */
  private async indexOrderInElasticsearch(order: IOrder): Promise<void> {
    if (!this.esClient) return;

    try {
      await this.esClient.index({
        index: process.env.ELASTICSEARCH_INDEX || 'befarma',
        id: order._id.toString(),
        body: {
          type: 'order',
          orderNumber: order.orderNumber,
          buyerId: order.buyerId.toString(),
          farmerId: order.sellerId.toString(),
          farmId: order.farmId?.toString(),
          totalAmount: order.totalAmount,
          orderStatus: order.orderStatus,
          paymentStatus: order.paymentStatus,
          orderDate: order.orderDate,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt
        }
      });
    } catch (error) {
      console.error('Error indexing order in Elasticsearch:', error);
      throw error;
    }
  }

  /**
   * Update order in Elasticsearch
   */
  private async updateOrderInElasticsearch(order: any): Promise<void> {
    if (!this.esClient) return;

    try {
      await this.esClient.update({
        index: process.env.ELASTICSEARCH_INDEX || 'befarma',
        id: order._id.toString(),
        body: {
          doc: {
            orderStatus: order.orderStatus,
            paymentStatus: order.paymentStatus,
            totalAmount: order.totalAmount,
            updatedAt: order.updatedAt
          }
        }
      });
    } catch (error) {
      console.error('Error updating order in Elasticsearch:', error);
      throw error;
    }
  }
}
