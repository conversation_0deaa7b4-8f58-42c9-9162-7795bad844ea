import { Request, Response, NextFunction } from 'express';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

/**
 * Middleware to validate request body against a JSON schema
 */
export const validateBody = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const validate = ajv.compile(schema);
    const valid = validate(req.body);

    if (!valid) {
      const errors = validate.errors?.map(error => ({
        field: error.instancePath.replace('/', '') || error.params?.missingProperty || 'root',
        message: error.message,
        value: error.data
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: errors
        },
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * Middleware to validate request parameters against a JSON schema
 */
export const validateParams = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const validate = ajv.compile(schema);
    const valid = validate(req.params);

    if (!valid) {
      const errors = validate.errors?.map(error => ({
        field: error.instancePath.replace('/', '') || error.params?.missingProperty || 'root',
        message: error.message,
        value: error.data
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Parameter validation failed',
          details: errors
        },
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * Middleware to validate request query parameters against a JSON schema
 */
export const validateQuery = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const validate = ajv.compile(schema);
    const valid = validate(req.query);

    if (!valid) {
      const errors = validate.errors?.map(error => ({
        field: error.instancePath.replace('/', '') || error.params?.missingProperty || 'root',
        message: error.message,
        value: error.data
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Query parameter validation failed',
          details: errors
        },
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};
