import rateLimit from 'express-rate-limit';

/**
 * Standard rate limiter for general API endpoints
 */
export const standardLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP, please try again later.'
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

/**
 * Strict rate limiter for order creation endpoints
 */
export const orderCreationLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // Limit each IP to 10 order creations per 5 minutes
  message: {
    success: false,
    error: {
      code: 'ORDER_CREATION_RATE_LIMIT_EXCEEDED',
      message: 'Too many order creation attempts, please try again later.'
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Moderate rate limiter for order updates
 */
export const orderUpdateLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 50, // Limit each IP to 50 order updates per 10 minutes
  message: {
    success: false,
    error: {
      code: 'ORDER_UPDATE_RATE_LIMIT_EXCEEDED',
      message: 'Too many order update attempts, please try again later.'
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Lenient rate limiter for read operations
 */
export const readLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // Limit each IP to 200 read requests per 15 minutes
  message: {
    success: false,
    error: {
      code: 'READ_RATE_LIMIT_EXCEEDED',
      message: 'Too many read requests, please try again later.'
    },
    timestamp: new Date().toISOString()
  },
  standardHeaders: true,
  legacyHeaders: false,
});
