import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

// Use the shared DecodedToken interface but extend it for our needs
interface ExtendedDecodedToken {
  id: string;
  role: string;
  iat: number;
  exp: number;
  email?: string;
  name?: string;
  sellerId?: string;
  buyerId?: string;
}

// Helper function to get extended user info from token
export const getExtendedUserInfo = (req: Request): ExtendedDecodedToken | null => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.split(' ')[1];
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    return null;
  }

  try {
    return jwt.verify(token, jwtSecret) as ExtendedDecodedToken;
  } catch (error) {
    return null;
  }
};

/**
 * Middleware to protect routes with JWT authentication
 */
export const protect = (req: Request, res: Response, next: NextFunction) => {
  try {
    let token: string | undefined;

    // Check for token in Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Access denied. No token provided.'
        },
        timestamp: new Date().toISOString()
      });
    }

    // Verify token
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not defined in environment variables');
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Server configuration error'
        },
        timestamp: new Date().toISOString()
      });
    }

    const decoded = jwt.verify(token, jwtSecret) as ExtendedDecodedToken;

    // Set user info on request object (compatible with shared DecodedToken interface)
    req.user = {
      id: decoded.id,
      role: decoded.role,
      iat: decoded.iat,
      exp: decoded.exp
    };

    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid token'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'TOKEN_EXPIRED',
          message: 'Token has expired'
        },
        timestamp: new Date().toISOString()
      });
    }

    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Authentication error'
      },
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Middleware to restrict access to specific roles
 */
export const restrictTo = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Access denied. Authentication required.'
        },
        timestamp: new Date().toISOString()
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Access denied. Insufficient permissions.'
        },
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * Middleware to check if user can access order (buyer or seller)
 */
export const checkOrderAccess = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: 'Access denied. Authentication required.'
      },
      timestamp: new Date().toISOString()
    });
  }

  // Admin can access all orders
  if (req.user.role === 'admin') {
    return next();
  }

  // For other operations, we'll check access in the controller
  // based on the order's buyerId and sellerId
  next();
};
