import { OrderStatus, PaymentStatus } from '../models/order.model';

// Order item DTO
export interface OrderItemDto {
  cropId: string;
  cropName: string;
  quantity: number;
  unitPrice: number;
  unit: string;
}

// Shipping address DTO
export interface ShippingAddressDto {
  fullName: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phoneNumber: string;
}

// Create order DTO
export interface CreateOrderDto {
  buyerId: string;
  farmerId: string;
  farmId?: string;
  items: OrderItemDto[];
  shippingCost?: number;
  tax?: number;
  currency?: string;
  paymentMethod?: string;
  shippingAddress: ShippingAddressDto;
  billingAddress?: ShippingAddressDto;
  expectedDeliveryDate?: Date;
  notes?: string;
}

// Update order DTO
export interface UpdateOrderDto {
  orderStatus?: OrderStatus;
  paymentStatus?: PaymentStatus;
  paymentTransactionId?: string;
  expectedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  trackingNumber?: string;
  notes?: string;
}

// Order query DTO
export interface OrderQueryDto {
  buyerId?: string;
  farmerId?: string;
  farmId?: string;
  orderStatus?: OrderStatus;
  paymentStatus?: PaymentStatus;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Order response DTO
export interface OrderResponseDto {
  id: string;
  orderNumber: string;
  buyerId: string;
  farmerId: string;
  farmId?: string;
  items: OrderItemDto[];
  subtotal: number;
  shippingCost: number;
  tax: number;
  totalAmount: number;
  currency: string;
  orderStatus: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod?: string;
  paymentTransactionId?: string;
  shippingAddress: ShippingAddressDto;
  billingAddress?: ShippingAddressDto;
  orderDate: Date;
  expectedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  notes?: string;
  trackingNumber?: string;
  orderAgeInDays?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Order statistics DTO
export interface OrderStatsDto {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  ordersByStatus: {
    [key in OrderStatus]: number;
  };
  ordersByPaymentStatus: {
    [key in PaymentStatus]: number;
  };
  recentOrders: OrderResponseDto[];
}

// Validation schemas for request validation
export const createOrderSchema = {
  type: 'object',
  required: ['buyerId', 'farmerId', 'items', 'shippingAddress'],
  properties: {
    buyerId: {
      type: 'string',
      pattern: '^[0-9a-fA-F]{24}$'
    },
    farmerId: {
      type: 'string',
      pattern: '^[0-9a-fA-F]{24}$'
    },
    farmId: {
      type: 'string',
      pattern: '^[0-9a-fA-F]{24}$'
    },
    items: {
      type: 'array',
      minItems: 1,
      items: {
        type: 'object',
        required: ['cropId', 'cropName', 'quantity', 'unitPrice', 'unit'],
        properties: {
          cropId: {
            type: 'string',
            pattern: '^[0-9a-fA-F]{24}$'
          },
          cropName: {
            type: 'string',
            minLength: 1,
            maxLength: 100
          },
          quantity: {
            type: 'number',
            minimum: 0.01
          },
          unitPrice: {
            type: 'number',
            minimum: 0
          },
          unit: {
            type: 'string',
            minLength: 1,
            maxLength: 20
          }
        }
      }
    },
    shippingCost: {
      type: 'number',
      minimum: 0
    },
    tax: {
      type: 'number',
      minimum: 0
    },
    currency: {
      type: 'string',
      enum: ['INR', 'USD', 'EUR']
    },
    paymentMethod: {
      type: 'string',
      maxLength: 50
    },
    shippingAddress: {
      type: 'object',
      required: ['fullName', 'addressLine1', 'city', 'state', 'postalCode', 'country', 'phoneNumber'],
      properties: {
        fullName: {
          type: 'string',
          minLength: 1,
          maxLength: 100
        },
        addressLine1: {
          type: 'string',
          minLength: 1,
          maxLength: 200
        },
        addressLine2: {
          type: 'string',
          maxLength: 200
        },
        city: {
          type: 'string',
          minLength: 1,
          maxLength: 50
        },
        state: {
          type: 'string',
          minLength: 1,
          maxLength: 50
        },
        postalCode: {
          type: 'string',
          minLength: 1,
          maxLength: 20
        },
        country: {
          type: 'string',
          minLength: 1,
          maxLength: 50
        },
        phoneNumber: {
          type: 'string',
          minLength: 10,
          maxLength: 15
        }
      }
    },
    billingAddress: {
      type: 'object',
      properties: {
        fullName: {
          type: 'string',
          minLength: 1,
          maxLength: 100
        },
        addressLine1: {
          type: 'string',
          minLength: 1,
          maxLength: 200
        },
        addressLine2: {
          type: 'string',
          maxLength: 200
        },
        city: {
          type: 'string',
          minLength: 1,
          maxLength: 50
        },
        state: {
          type: 'string',
          minLength: 1,
          maxLength: 50
        },
        postalCode: {
          type: 'string',
          minLength: 1,
          maxLength: 20
        },
        country: {
          type: 'string',
          minLength: 1,
          maxLength: 50
        },
        phoneNumber: {
          type: 'string',
          minLength: 10,
          maxLength: 15
        }
      }
    },
    expectedDeliveryDate: {
      type: 'string',
      format: 'date-time'
    },
    notes: {
      type: 'string',
      maxLength: 1000
    }
  }
};

export const updateOrderSchema = {
  type: 'object',
  properties: {
    orderStatus: {
      type: 'string',
      enum: Object.values(OrderStatus)
    },
    paymentStatus: {
      type: 'string',
      enum: Object.values(PaymentStatus)
    },
    paymentTransactionId: {
      type: 'string',
      maxLength: 100
    },
    expectedDeliveryDate: {
      type: 'string',
      format: 'date-time'
    },
    actualDeliveryDate: {
      type: 'string',
      format: 'date-time'
    },
    trackingNumber: {
      type: 'string',
      maxLength: 100
    },
    notes: {
      type: 'string',
      maxLength: 1000
    }
  }
};

export const orderIdParamSchema = {
  type: 'object',
  required: ['orderId'],
  properties: {
    orderId: {
      type: 'string',
      pattern: '^[0-9a-fA-F]{24}$'
    }
  }
};
