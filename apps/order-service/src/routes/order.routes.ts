import { Router } from 'express';
import { OrderController } from '../controllers/order.controller';
import { protect, restrictTo, checkOrderAccess } from '../middleware/auth.middleware';
import { validateBody, validateParams } from '../middleware/validation.middleware';
import { 
  standardLimiter, 
  orderCreationLimiter, 
  orderUpdateLimiter, 
  readLimiter 
} from '../middleware/rate-limiter.middleware';
import { 
  createOrderSchema, 
  updateOrderSchema, 
  orderIdParamSchema 
} from '../dto/order.dto';

export const setupOrderRoutes = (esClient?: any): Router => {
  const router = Router();
  const orderController = new OrderController(esClient);

  // Public routes (no authentication required)
  
  // Protected routes (authentication required)
  
  // Create order - requires authentication
  router.post(
    '/',
    orderCreationLimiter,
    protect,
    validateBody(createOrderSchema),
    orderController.createOrder
  );

  // Get orders with filtering and pagination - requires authentication
  router.get(
    '/',
    readLimiter,
    protect,
    orderController.getOrders
  );

  // Get order statistics - requires authentication
  router.get(
    '/stats',
    readLimiter,
    protect,
    orderController.getOrderStats
  );

  // Get order by ID - requires authentication
  router.get(
    '/:orderId',
    readLimiter,
    protect,
    validateParams(orderIdParamSchema),
    checkOrderAccess,
    orderController.getOrderById
  );

  // Get order by order number - requires authentication
  router.get(
    '/number/:orderNumber',
    readLimiter,
    protect,
    checkOrderAccess,
    orderController.getOrderByNumber
  );

  // Update order - requires authentication
  router.put(
    '/:orderId',
    orderUpdateLimiter,
    protect,
    validateParams(orderIdParamSchema),
    validateBody(updateOrderSchema),
    checkOrderAccess,
    orderController.updateOrder
  );

  // Update order status - requires authentication
  router.patch(
    '/:orderId/status',
    orderUpdateLimiter,
    protect,
    validateParams(orderIdParamSchema),
    checkOrderAccess,
    orderController.updateOrderStatus
  );

  // Cancel order - requires authentication
  router.patch(
    '/:orderId/cancel',
    orderUpdateLimiter,
    protect,
    validateParams(orderIdParamSchema),
    checkOrderAccess,
    orderController.cancelOrder
  );

  // Admin only routes
  
  // Get all orders (admin only)
  router.get(
    '/admin/all',
    readLimiter,
    protect,
    restrictTo('admin'),
    orderController.getOrders
  );

  // Get comprehensive order statistics (admin only)
  router.get(
    '/admin/stats',
    readLimiter,
    protect,
    restrictTo('admin'),
    orderController.getOrderStats
  );

  // Update any order (admin only)
  router.put(
    '/admin/:orderId',
    standardLimiter,
    protect,
    restrictTo('admin'),
    validateParams(orderIdParamSchema),
    validateBody(updateOrderSchema),
    orderController.updateOrder
  );

  return router;
};
