import express from 'express';
import * as path from 'path';
import mongoose from 'mongoose';
import cors from 'cors';
import helmet from 'helmet';
import { json } from 'body-parser';
import { initializeDbConnections, closeDbConnections } from './config/db';
import { setupOrderRoutes } from './routes/order.routes';
import dotenv from 'dotenv';

// Load environment variables based on NODE_ENV
const envFile = process.env.NODE_ENV === 'production'
  ? '.env.production'
  : process.env.NODE_ENV === 'development'
    ? '.env.development'
    : '.env.local';

dotenv.config({ path: envFile });

// Initialize Express app
const app = express();

// Middleware
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
  credentials: true,
}));
app.use(helmet());
app.use(json({ limit: '10mb' }));
app.use('/assets', express.static(path.join(__dirname, 'assets')));

// Initialize database connections and routes
const initializeApp = async () => {
  try {
    // Initialize database connections
    const esClient = await initializeDbConnections();

    // Set up API routes
    app.use('/api/orders', setupOrderRoutes(esClient));

    app.get('/api', (req, res) => {
      res.json({
        message: 'Welcome to AgriTech Order Service!',
        version: '1.0.0',
        service: 'order-service',
        port: process.env.PORT || 3009,
        endpoints: {
          health: '/health',
          orders: '/api/orders',
          orderStats: '/api/orders/stats',
          adminOrders: '/api/orders/admin/all',
        },
        timestamp: new Date().toISOString()
      });
    });

    app.get('/api/health', (req, res) => {
      res.status(200).json({
        status: 'UP',
        service: 'order-service',
        database: mongoose.connection ? (mongoose.connection.readyState === 1 ? 'connected' : 'disconnected') : 'not applicable',
        elasticsearch: esClient ? 'connected' : 'not available',
        timestamp: new Date().toISOString()
      });
    });

    // 404 handler for undefined routes
    app.all('*', (req, res, next) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `Cannot find ${req.method} ${req.originalUrl} on this server`,
        },
        timestamp: new Date().toISOString()
      });
    });

    // Error handling middleware
    app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      console.error('Unhandled error:', err);

      res.status(err.status || 500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: err.message || 'Internal Server Error'
        },
        timestamp: new Date().toISOString(),
        path: req.path
      });
    });

    // Start the server
    const port = process.env.PORT || 3009;
    const server = app.listen(port, () => {
      console.log(`🛒 Order Service listening at http://localhost:${port}`);
      console.log(`📋 Health check available at http://localhost:${port}/api/health`);
      console.log(`📚 API documentation: http://localhost:${port}/api`);
    });

    server.on('error', console.error);

    // Handle graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM signal received: closing HTTP server');
      server.close(async () => {
        console.log('HTTP server closed');
        // Close database connections
        await closeDbConnections(esClient);
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      console.log('SIGINT signal received: closing HTTP server');
      server.close(async () => {
        console.log('HTTP server closed');
        // Close database connections
        await closeDbConnections(esClient);
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('Application initialization failed:', error);

    // Close any existing connections before exiting
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }

    process.exit(1);
  }
};

initializeApp();
