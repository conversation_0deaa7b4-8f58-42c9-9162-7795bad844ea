SERVICE_NAME=order-service

# AgriTech Seller Backend Environment Configuration

# ================================
# APPLICATION CONFIGURATION
# ================================
NODE_ENV=production
PORT=3009

# ================================
# MONGODB CONFIGURATION
# ================================
MONGODB_URI=******************************************************
MONGODB_CONNECTION_TIMEOUT=30000
MONGODB_POOL_SIZE=10

# ================================
# ELASTICSEARCH CONFIGURATION
# ================================
ELASTICSEARCH_NODE=befarma-f44523.es.us-east-1.aws.elastic.cloud:443
ELASTICSEARCH_INDEX=befarma
ELASTICSEARCH_API_KEY=your_api_key_here
ELASTICSEARCH_SERVERLESS=true

# ================================
# JWT CONFIGURATION
# ================================
JWT_SECRET=befarma_order_service_secret_key_2024
JWT_EXPIRES_IN=24h

# ================================
# CORS CONFIGURATION
# ================================
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4200
