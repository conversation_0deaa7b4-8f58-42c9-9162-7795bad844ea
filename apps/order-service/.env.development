SERVICE_NAME=order-service

# AgriTech Seller Backend Environment Configuration

# ================================
# APPLICATION CONFIGURATION
# ================================
NODE_ENV=development
PORT=3009

# ================================
# MONGODB CONFIGURATION
# ================================
MONGODB_URI=******************************************************
# MONGODB_CONNECTION_TIMEOUT=30000
# MONGODB_POOL_SIZE=10

# ================================
# ELASTICSEARCH CONFIGURATION
# ================================
# New Elasticsearch Configuration
ELASTICSEARCH_NODE=https://34.47.146.9:9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=rTAubs+yplYX9sbXH-E7
ELASTICSEARCH_INDEX=befarma
ELASTICSEARCH_SERVERLESS=false
ELASTICSEARCH_SSL_VERIFY=false

# Legacy API Key Configuration (Fallback)
# ELASTICSEARCH_API_KEY=YOUR_API_KEY_HERE

# ================================
# JWT CONFIGURATION
# ================================

# JWT Configuration
JWT_SECRET=befarma_
JWT_EXPIRES_IN=24h

# ================================
# CORS CONFIGURATION
# ================================
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4200
