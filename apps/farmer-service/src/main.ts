import express from 'express';
import * as path from 'path';
import cors from 'cors';
import { json } from 'body-parser';
import helmet from 'helmet';
import farmerRoutes from './routes/farmer.routes';
import { dbManager, MongoDBConfig } from '@libs/shared';
import { errorHandler } from './middleware/error-handler.middleware';
import { standardLimiter } from './middleware/rate-limiter.middleware';
import { requestLogger, errorLogger } from './middleware/logger.middleware';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// MongoDB URI from environment variables
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/agritech';
const PORT = process.env.PORT || 3001; // Farmer service runs on port 3001

// Start the server
async function bootstrap() {
  // Create Express app
  const app = express();

  try {
    // Connect to MongoDB using the shared connector
    const mongoConfig: MongoDBConfig = {
      uri: MONGODB_URI,
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
      debug: process.env.NODE_ENV !== 'production'
    };
    
    await dbManager.initMongoDB(mongoConfig);
    console.log('Connected to MongoDB using shared connector');
    
    // Trust proxy for proper IP detection behind reverse proxy
    app.set('trust proxy', 1);
    
    // Request logger middleware (should be at the top)
    app.use(requestLogger);
    app.use(helmet()); // Add security headers
    app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
      credentials: true,
    })); // Enable CORS
    app.use(json({ limit: '10mb' })); // Body parser with increased payload limit for document uploads
    app.use(standardLimiter); // Rate limiting
    
    // Static files
    app.use('/assets', express.static(path.join(__dirname, 'assets')));
    
    // Health check endpoint (no rate limit)
    app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'ok',
        service: 'farmer-service',
        port: PORT,
        timestamp: new Date().toISOString(),
        database: 'connected'
      });
    });
    
    // API Routes
    app.use('/api/v1/farmers', farmerRoutes);
    
    // Root endpoint
    // app.get('/api/v1', (req, res) => {
    //   res.json({ 
    //     message: 'Welcome to AgriTech Seller Service!',
    //     version: '1.0.0',
    //     service: 'seller-service',
    //     port: PORT,
    //     endpoints: {
    //       health: '/health',
    //       sellers: '/api/v1/sellers',
    //       auth: '/api/v1/sellers/auth',
    //       onboarding: '/api/v1/sellers/onboarding',
    //       profile: '/api/v1/sellers/profile',
    //     },
    //     timestamp: new Date().toISOString()
    //   });
    // });
    
    // 404 handler for undefined routes
    app.all('*', (req, res, next) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `Cannot find ${req.method} ${req.originalUrl} on this server`,
        },
        timestamp: new Date().toISOString()
      });
    });
    
    // Error logger middleware (should be before error handler)
    app.use(errorLogger);
    
    // Error handler middleware (should be after routes)
    app.use(errorHandler);

    // Server setup
    const server = app.listen(PORT, () => {
      console.log(`🌾 Farmer Service listening at http://localhost:${PORT}`);
      console.log(`📋 Health check available at http://localhost:${PORT}/health`);
      console.log(`📚 API documentation: http://localhost:${PORT}/api/v1`);
    });
    
    // Graceful shutdown handlers
    const gracefulShutdown = async (signal: string) => {
      console.log(`${signal} received. Shutting down gracefully...`);
      
      try {
        // Close database connections
        await dbManager.closeAll();
        console.log('✅ Disconnected from MongoDB');
        
        // Close server
        server.close(() => {
          console.log('✅ Farmer Service terminated gracefully');
          process.exit(0);
        });
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };
    
    // Handle unhandled rejections
    process.on('unhandledRejection', async (err) => {
      console.error('❌ UNHANDLED REJECTION! Shutting down...');
      console.error(err);
      await gracefulShutdown('UNHANDLED_REJECTION');
    });
    
    // Handle SIGTERM & SIGINT signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    server.on('error', (error: any) => {
      console.error('❌ Server error:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`Port ${PORT} is already in use. Please check if another instance is running.`);
      }
    });
    
  } catch (error) {
    console.error('❌ Failed to start Farmer Service:', error);
    process.exit(1);
  }
}

// Start the server
bootstrap();
