import { Router } from 'express';
import { FarmerOnboardingController } from '../controllers/farmer-onboarding.controller';
import { FarmerProfileController } from '../controllers/farmer-profile.controller';
import { AuthController } from '../controllers/auth.controller';
import { KYCController } from '../controllers/kyc.controller';
import { FarmManagementController } from '../controllers/farm-management.controller';
import { DropdownController } from '../controllers/dropdown.controller';
import { protect } from '../middleware/auth.middleware';
import { validateBody, validateParams } from '../middleware/validation.middleware';
import { standardLimiter, authLimiter, registrationLimiter } from '../middleware/rate-limiter.middleware';
import {
  registerFarmerSchema,
  farmerDocumentsSchema,
  verificationStatusSchema,
  updateProfileSchema,
  bankDetailsSchema,
  loginSchema,
  farmerIdParamSchema,
} from '../dto/validation.schemas';
import multer from 'multer';
import * as path from 'path';

const router = Router();
const onboardingController = new FarmerOnboardingController();
const profileController = new FarmerProfileController();
const authController = new AuthController();
const kycController = new KYCController();
const farmManagementController = new FarmManagementController();
const dropdownController = new DropdownController();

// Multer storage configuration for KYC documents
const kycStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../assets/kyc/'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, uniqueSuffix + '-' + file.originalname.replace(/\s+/g, '_'));
  },
});

const kycUpload = multer({
  storage: kycStorage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    // Accept images and PDFs only
    if (/\.(jpg|jpeg|png|pdf)$/i.test(file.originalname)) {
      cb(null, true);
    } else {
      cb(new Error('Only .jpg, .jpeg, .png, and .pdf files are allowed!'));
    }
  },
});

// Auth Routes
router.post('/login', authLimiter, validateBody(loginSchema), authController.login);

// Farmer Onboarding Routes
router.post('/register', registrationLimiter, validateBody(registerFarmerSchema), onboardingController.registerFarmer);
router.post(
  '/documents/:farmerId',
  standardLimiter,
  validateParams(farmerIdParamSchema),
  protect,
  kycUpload.single('document'), // Accept a single file with field name 'document'
  onboardingController.uploadDocuments
);
router.post(
  '/verify',
  standardLimiter,
  validateBody(verificationStatusSchema),
  protect,
  onboardingController.updateVerificationStatus
);
router.post(
  '/activate/:farmerId',
  standardLimiter,
  validateParams(farmerIdParamSchema),
  protect,
  onboardingController.activateAccount
);
router.get(
  '/background-check/:farmerId',
  standardLimiter,
  validateParams(farmerIdParamSchema),
  protect,
  onboardingController.performBackgroundCheck
);

// KYC Routes
router.post('/kyc/submit', standardLimiter, protect, kycController.submitKYC);
router.get('/kyc/status', standardLimiter, protect, kycController.getKYCStatus);
router.get('/dashboard', standardLimiter, protect, kycController.getFarmerDashboard);

// Admin KYC Routes (require admin permissions)
router.get('/kyc/admin/all', standardLimiter, protect, kycController.getAllFarmersWithKYC);
router.put('/kyc/admin/verify/:farmerId', standardLimiter, validateParams(farmerIdParamSchema), protect, kycController.verifyKYCDocuments);
router.put('/admin/status', standardLimiter, protect, kycController.updateFarmerStatus);

// Farmer Profile Routes
router.get(
  '/profile',
  standardLimiter,
  protect,
  profileController.getProfile
);
router.put(
  '/profile',
  standardLimiter,
  validateBody(updateProfileSchema),
  protect,
  profileController.updateProfile
);
router.put(
  '/profile/bank-details',
  standardLimiter,
  validateBody(bankDetailsSchema),
  protect,
  profileController.updateBankDetails
);
router.get('/', standardLimiter, protect, profileController.getAllFarmers);
router.delete(
  '/profile',
  standardLimiter,
  protect,
  profileController.deleteAccount
);

// Farm Management Routes
router.post(
  '/farms',
  standardLimiter,
  protect,
  farmManagementController.createFarm
);

router.get(
  '/farms',
  standardLimiter,
  protect,
  farmManagementController.getFarmerFarms
);

router.get(
  '/farms/:farmId',
  standardLimiter,
  protect,
  farmManagementController.getFarmById
);

router.put(
  '/farms/:farmId',
  standardLimiter,
  protect,
  farmManagementController.updateFarm
);

router.put(
  '/farms/:farmId/rotation',
  standardLimiter,
  protect,
  farmManagementController.updateCropRotation
);

router.get(
  '/farms/:farmId/analytics',
  standardLimiter,
  protect,
  farmManagementController.getFarmAnalytics
);

router.get(
  '/farms/search',
  standardLimiter,
  protect,
  farmManagementController.searchFarms
);

router.delete(
  '/farms/:farmId',
  standardLimiter,
  protect,
  farmManagementController.deleteFarm
);

// Dropdown Routes
router.get(
  '/dropdowns/farms',
  standardLimiter,
  protect,
  dropdownController.getFarmsDropdown
);

router.get(
  '/dropdowns/farms/:farmerId',
  standardLimiter,
  validateParams(farmerIdParamSchema),
  protect,
  dropdownController.getFarmsDropdownByFarmer
);

router.get(
  '/dropdowns/crops',
  standardLimiter,
  protect,
  dropdownController.getCropsDropdown
);

router.get(
  '/dropdowns/enums/:category/:subcategory',
  standardLimiter,
  protect,
  dropdownController.getEnumDropdown
);

router.get(
  '/dropdowns/enums',
  standardLimiter,
  protect,
  dropdownController.getAvailableEnums
);

export default router;