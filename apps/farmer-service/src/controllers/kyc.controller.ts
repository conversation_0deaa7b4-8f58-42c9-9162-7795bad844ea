import { Request, Response } from 'express';
import { KYCService } from '../services/kyc.service';
import { KYCSubmissionDto, SellerStatusDto, FarmerStatusDto } from '../dto/kyc.dto';
import { dbManager } from '@libs/shared';

export class KYCController {
  private kycService: KYCService;

  constructor() {
    this.kycService = new KYCService(dbManager.getMongoDBConnector()!);
  }

  /**
   * Submit KYC documents for verification
   */
  submitKYC = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get seller ID from the authenticated user's token instead of request body
      if (!req.user || !req.user.id) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'Authentication required'
          }
        });
        return;
      }

      const kycData: KYCSubmissionDto = {
        ...req.body,
        sellerId: req.user.id // Override sellerId with authenticated user's ID
      };

      // Validate required fields
      if (!kycData.documents || !kycData.personalDetails) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Missing required fields: documents or personalDetails'
          }
        });
        return;
      }
      // Ensure each document has a non-empty documentUrl (file URL from upload step)
      if (!Array.isArray(kycData.documents) || kycData.documents.some(doc => !doc.documentUrl)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Each document must include a valid documentUrl (file URL from upload endpoint)'
          }
        });
        return;
      }

      const result = await this.kycService.submitKYCDocuments(kycData);

      if (result.success) {
        res.status(201).json({
          success: true,
          data: {
            kycId: result.kycId,
            message: result.message
          }
        });
      } else {
        res.status(400).json({
          success: false,
          error: {
            code: 'SUBMISSION_FAILED',
            message: result.message
          }
        });
      }
    } catch (error) {
      console.error('Error submitting KYC:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to submit KYC documents'
        }
      });
    }
  };

  /**
   * Get KYC status for a seller
   */
  getKYCStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get seller ID from the authenticated user's token instead of URL params
      if (!req.user || !req.user.id) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'Authentication required'
          }
        });
        return;
      }

      const sellerId = req.user.id;
      const kycStatus = await this.kycService.getKYCStatus(sellerId);

      if (kycStatus) {
        res.status(200).json({
          success: true,
          data: kycStatus
        });
      } else {
        res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Seller not found'
          }
        });
      }
    } catch (error) {
      console.error('Error getting KYC status:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get KYC status'
        }
      });
    }
  };

  /**
   * Update farmer status (admin function)
   */
  updateFarmerStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const statusData: FarmerStatusDto = req.body;

      // Validate required fields
      if (!statusData.farmerId || !statusData.status || !statusData.updatedBy) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Missing required fields: farmerId, status, or updatedBy'
          }
        });
        return;
      }

      const result = await this.kycService.updateFarmerStatus(statusData);
      
      if (result.success) {
        res.status(200).json({
          success: true,
          data: {
            message: result.message
          }
        });
      } else {
        res.status(400).json({
          success: false,
          error: {
            code: 'UPDATE_FAILED',
            message: result.message
          }
        });
      }
    } catch (error) {
      console.error('Error updating seller status:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update seller status'
        }
      });
    }
  };

  /**
   * Get seller dashboard data
   */
  getFarmerDashboard = async (req: Request, res: Response): Promise<void> => {
    try {
      // Get seller ID from the authenticated user's token instead of URL params
      if (!req.user || !req.user.id) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: 'Authentication required'
          }
        });
        return;
      }

      const sellerId = req.user.id;
      const dashboard = await this.kycService.getFarmerDashboard(sellerId);

      if (dashboard) {
        res.status(200).json({
          success: true,
          data: dashboard
        });
      } else {
        res.status(404).json({
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Seller not found'
          }
        });
      }
    } catch (error) {
      console.error('Error getting seller dashboard:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get seller dashboard'
        }
      });
    }
  };

  /**
   * Get all sellers with KYC status (admin function)
   */
  getAllFarmersWithKYC = async (req: Request, res: Response): Promise<void> => {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      
      if (page < 1 || limit < 1 || limit > 100) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 100'
          }
        });
        return;
      }

      const result = await this.kycService.getAllFarmersWithKYC(page, limit);
      
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Error getting farmers with KYC:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get farmers with KYC status'
        }
      });
    }
  };

  /**
   * Verify KYC documents (admin function)
   */
  verifyKYCDocuments = async (req: Request, res: Response): Promise<void> => {
    try {
      const { farmerId } = req.params;
      const { approved, notes, verifiedBy } = req.body;

      // Validate required fields
      if (!farmerId || approved === undefined || !verifiedBy) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Missing required fields: farmerId, approved (boolean), or verifiedBy'
          }
        });
        return;
      }

      const result = await this.kycService.verifyKYCDocuments(farmerId, verifiedBy, approved, notes);
      
      if (result.success) {
        res.status(200).json({
          success: true,
          data: {
            message: result.message
          }
        });
      } else {
        res.status(400).json({
          success: false,
          error: {
            code: 'VERIFICATION_FAILED',
            message: result.message
          }
        });
      }
    } catch (error) {
      console.error('Error verifying KYC documents:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to verify KYC documents'
        }
      });
    }
  };
} 