import { Request, Response } from 'express';
import { FarmerOnboardingService } from '../services/farmer-onboarding.service';
import { RegisterFarmerDto, FarmerDocumentsDto, UpdateVerificationStatusDto } from '../dto/farmer.dto';
import { MongoDBConnector } from '@libs/shared';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize MongoDB connector
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/agritech';
const DB_NAME = process.env.DB_NAME || 'agritech';
const dbConnector = MongoDBConnector.getInstance({
  uri: MONGODB_URI,
  options: { dbName: DB_NAME }
});
const farmerOnboardingService = new FarmerOnboardingService(dbConnector);

export class FarmerOnboardingController {
  /**
   * Register a new farmer
   * @param req Express request
   * @param res Express response
   */
  async registerFarmer(req: Request, res: Response): Promise<void> {
    try {
      const registerDto: RegisterFarmerDto = req.body;
      
      // Validate required fields
      if (!registerDto.name || !registerDto.email || !registerDto.contact || !registerDto.address || !registerDto.password) {
        res.status(400).json({ message: 'Missing required fields' });
        return;
      }
      
      const farmer = await farmerOnboardingService.registerFarmer(registerDto);
      res.status(201).json(farmer);
    } catch (error) {
      console.error('Error in registerSeller controller:', error);
      if (error.message === 'Email already registered') {
        res.status(409).json({ message: error.message });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  }
  
  /**
   * Upload seller documents
   * @param req Express request
   * @param res Express response
   */
  async uploadDocuments(req: Request, res: Response): Promise<void> {
    try {
      const farmerId = req.params.farmerId;
      // If file is uploaded via multer
      if (req.file) {
        // Build the file URL (assuming static serving from /assets/kyc/)
        const fileUrl = `/assets/kyc/${req.file.filename}`;
        // Optionally, get documentType from req.body
        const documentType = req.body.documentType || 'identityProof';
        // Build the documentsDto accordingly
        const documentsDto: SellerDocumentsDto = {
          identityProof: documentType === 'identityProof' ? fileUrl : '',
          landOwnership: documentType === 'landOwnership' ? fileUrl : '',
          certifications: documentType === 'certification' ? [fileUrl] : [],
        };
        const updatedFarmer = await farmerOnboardingService.uploadDocuments(farmerId, documentsDto);
        if (!updatedFarmer) {
          res.status(404).json({ message: 'Farmer not found' });
          return;
        }
        res.status(200).json({
          message: 'File uploaded successfully',
          fileUrl,
          farmer: updatedFarmer,
        });
        return;
      }
      // Legacy: Accept document URLs in body
      const documentsDto: SellerDocumentsDto = req.body;
      if (!documentsDto.identityProof || !documentsDto.landOwnership) {
        res.status(400).json({ message: 'Missing required document files' });
        return;
      }
      const updatedFarmer = await farmerOnboardingService.uploadDocuments(farmerId, documentsDto);
      if (!updatedFarmer) {
        res.status(404).json({ message: 'Farmer not found' });
        return;
      }
      res.status(200).json(updatedFarmer);
    } catch (error) {
      console.error('Error in uploadDocuments controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
  
  /**
   * Update seller verification status
   * @param req Express request
   * @param res Express response
   */
  async updateVerificationStatus(req: Request, res: Response): Promise<void> {
    try {
      const statusDto: UpdateVerificationStatusDto = req.body;
      
      // Validate required fields
      if (!statusDto.farmerId || !statusDto.status) {
        res.status(400).json({ message: 'Missing required fields' });
        return;
      }
      
      const updatedFarmer = await farmerOnboardingService.updateVerificationStatus(statusDto);

      if (!updatedFarmer) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }
      
      res.status(200).json(updatedFarmer);
    } catch (error) {
      console.error('Error in updateVerificationStatus controller:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
  
  /**
   * Activate seller account
   * @param req Express request
   * @param res Express response
   */
  async activateAccount(req: Request, res: Response): Promise<void> {
    try {
      const farmerId = req.params.farmerId;

      const activatedFarmer = await farmerOnboardingService.activateAccount(farmerId);

      if (!activatedFarmer) {
        res.status(404).json({ message: 'Seller not found' });
        return;
      }
      
      res.status(200).json(activatedFarmer);
    } catch (error) {
      console.error('Error in activateAccount controller:', error);
      if (error.message === 'Seller must be verified before activation') {
        res.status(400).json({ message: error.message });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  }
  
  /**
   * Perform background check
   * @param req Express request
   * @param res Express response
   */
  async performBackgroundCheck(req: Request, res: Response): Promise<void> {
    try {
      const farmerId = req.params.farmerId;

      const result = await farmerOnboardingService.performBackgroundCheck(farmerId);
      
      res.status(200).json(result);
    } catch (error) {
      console.error('Error in performBackgroundCheck controller:', error);
      if (error.message === 'Seller not found') {
        res.status(404).json({ message: error.message });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  }
}

// Backward compatibility export
export const SellerOnboardingController = FarmerOnboardingController;