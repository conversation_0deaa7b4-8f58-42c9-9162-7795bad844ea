import { FarmerBankDetailsDto, UpdateFarmerProfileDto } from '../dto/farmer.dto';
import {
  FarmerRepository,
  FarmerDocument,
  MongoDBConnector,
  dbManager
} from '@libs/shared';

export class FarmerProfileService {
  private farmerRepository: FarmerRepository;

  constructor(dbConnector: MongoDBConnector) {
    this.farmerRepository = new FarmerRepository(dbConnector);
  }

  /**
   * Get farmer profile by ID
   * @param farmerId Farmer ID
   * @returns Farmer profile
   */
  async getProfile(farmerId: string): Promise<FarmerDocument | null> {
    try {
      return await this.farmerRepository.findById(farmerId);
    } catch (error) {
      console.error('Error getting farmer profile:', error);
      throw error;
    }
  }
  
  /**
   * Update farmer profile
   * @param farmerId Farmer ID
   * @param profileDto Profile update data
   * @returns Updated farmer profile
   */
  async updateProfile(farmerId: string, profileDto: UpdateFarmerProfileDto): Promise<FarmerDocument | null> {
    try {
      // Check if farmer exists
      const farmer = await this.farmerRepository.findById(farmerId);
      if (!farmer) {
        throw new Error('Farmer not found');
      }

      // Create update object with only the fields that are provided
      const updateData: any = {};

      if (profileDto.name) {
        updateData['personalInfo.name'] = profileDto.name;
      }

      if (profileDto.contact) {
        updateData['personalInfo.contact'] = profileDto.contact;
      }

      if (profileDto.email) {
        // Check if the new email is already in use by another farmer
        if (profileDto.email !== farmer.personalInfo.email) {
          const existingFarmer = await this.farmerRepository.findByEmail(profileDto.email);
          if (existingFarmer && existingFarmer.farmerId !== farmerId) {
            throw new Error('Email already in use by another farmer');
          }
        }
        updateData['personalInfo.email'] = profileDto.email;
      }

      if (profileDto.address) {
        if (profileDto.address.country) {
          updateData['personalInfo.address.country'] = profileDto.address.country;
        }
        if (profileDto.address.state) {
          updateData['personalInfo.address.state'] = profileDto.address.state;
        }
        if (profileDto.address.city) {
          updateData['personalInfo.address.city'] = profileDto.address.city;
        }
        if (profileDto.address.pincode) {
          updateData['personalInfo.address.pincode'] = profileDto.address.pincode;
        }
        if (profileDto.address.addressLine1) {
          updateData['personalInfo.address.addressLine1'] = profileDto.address.addressLine1;
        }
        if (profileDto.address.addressLine2) {
          updateData['personalInfo.address.addressLine2'] = profileDto.address.addressLine2;
        }
      }

      // Return updated farmer
      return await this.farmerRepository.update(farmerId, updateData);
    } catch (error) {
      console.error('Error updating farmer profile:', error);
      throw error;
    }
  }
  
  /**
   * Update farmer bank details
   * @param farmerId Farmer ID
   * @param bankDetailsDto Bank details data
   * @returns Updated farmer profile
   */
  async updateBankDetails(farmerId: string, bankDetailsDto: FarmerBankDetailsDto): Promise<FarmerDocument | null> {
    try {
      // Check if farmer exists
      const farmer = await this.farmerRepository.findById(farmerId);
      if (!farmer) {
        throw new Error('Farmer not found');
      }

      // Update bank details
      return await this.farmerRepository.update(farmerId, {
        bankDetails: {
          accountNumber: bankDetailsDto.accountNumber,
          bankName: bankDetailsDto.bankName,
          ifscCode: bankDetailsDto.ifscCode,
        },
      });
    } catch (error) {
      console.error('Error updating bank details:', error);
      throw error;
    }
  }

  /**
   * Get all farmers with pagination
   * @param page Page number
   * @param limit Items per page
   * @returns Paginated farmers
   */
  async getAllFarmers(page: number = 1, limit: number = 10): Promise<{ farmers: FarmerDocument[]; total: number; pages: number }> {
    try {
      return await this.farmerRepository.findAll(page, limit);
    } catch (error) {
      console.error('Error getting all farmers:', error);
      throw error;
    }
  }

  /**
   * Delete farmer account
   * @param farmerId Farmer ID
   * @returns True if deleted, false otherwise
   */
  async deleteAccount(farmerId: string): Promise<boolean> {
    try {
      return await this.farmerRepository.delete(farmerId);
    } catch (error) {
      console.error('Error deleting farmer account:', error);
      throw error;
    }
  }
} 