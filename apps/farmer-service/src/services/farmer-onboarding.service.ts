import { RegisterFarmerDto, FarmerDocumentsDto, UpdateVerificationStatusDto } from '../dto/farmer.dto';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import {
  FarmerDocument,
  FarmerRepository,
  MongoDBConnector
} from '@libs/shared';

export class FarmerOnboardingService {
  private farmerRepository: FarmerRepository;

  constructor(dbConnector: MongoDBConnector) {
    this.farmerRepository = new FarmerRepository(dbConnector);
  }

  /**
   * Register a new farmer
   * @param registerDto Farmer registration data
   * @returns Registered farmer
   */
  async registerFarmer(registerDto: RegisterFarmerDto): Promise<FarmerDocument> {
    try {
      // Check if email already exists
      const existingFarmer = await this.farmerRepository.findByEmail(registerDto.email);
      if (existingFarmer) {
        throw new Error('Email already registered');
      }

      // Hash password (in a real implementation, you'd store this in a user/auth service)
      const hashedPassword = await bcrypt.hash(registerDto.password, 10);

      // Generate farmer ID
      const farmerId = `FARMER-${uuidv4().split('-')[0].toUpperCase()}`;

      // Create farmer object with basic info
      const farmerData: any = {
        farmerId,
        personalInfo: {
          name: registerDto.name,
          email: registerDto.email,
          contact: registerDto.contact,
          address: {
            country: registerDto.address.country || 'INDIA',
            state: registerDto.address.state,
            city: registerDto.address.city,
            pincode: registerDto.address.pincode,
            addressLine1: registerDto.address.addressLine1,
            addressLine2: registerDto.address.addressLine2 || '',
          },
        },
        status: 'PENDING' as 'PENDING',
        verificationStatus: 'PENDING' as 'PENDING',
        statusHistory: [{
          status: 'PENDING',
          updatedBy: 'SYSTEM',
          reason: 'Initial registration',
          updatedAt: new Date()
        }]
      };
      
      // Add optional fields if provided
      if (registerDto.documents) {
        farmerData.documents = registerDto.documents;
      }

      if (registerDto.bankDetails) {
        farmerData.bankDetails = registerDto.bankDetails;
      }

      if (registerDto.farmingInfo) {
        farmerData.farmingInfo = registerDto.farmingInfo;
      }

      // Create farmer
      return await this.farmerRepository.create(farmerData);
    } catch (error) {
      console.error('Error registering farmer:', error);
      throw error;
    }
  }
  
  /**
   * Upload farmer verification documents
   * @param farmerId Farmer ID
   * @param documentsDto Document data
   * @returns Updated farmer
   */
  async uploadDocuments(farmerId: string, documentsDto: FarmerDocumentsDto): Promise<FarmerDocument | null> {
    try {
      // Check if farmer exists
      const farmer = await this.farmerRepository.findById(farmerId);
      if (!farmer) {
        throw new Error('Farmer not found');
      }

      // Update documents
      return await this.farmerRepository.update(farmerId, {
        documents: {
          identityProof: documentsDto.identityProof,
          landOwnership: documentsDto.landOwnership,
          certifications: documentsDto.certifications || [],
        },
      });
    } catch (error) {
      console.error('Error uploading documents:', error);
      throw error;
    }
  }
  
  /**
   * Update farmer verification status
   * @param statusDto Verification status data
   * @returns Updated farmer
   */
  async updateVerificationStatus(statusDto: UpdateVerificationStatusDto): Promise<FarmerDocument | null> {
    try {
      // Check if farmer exists
      const farmer = await this.farmerRepository.findById(statusDto.farmerId);
      if (!farmer) {
        throw new Error('Farmer not found');
      }

      // Update verification status
      return await this.farmerRepository.updateVerificationStatus(
        statusDto.farmerId,
        statusDto.status
      );
    } catch (error) {
      console.error('Error updating verification status:', error);
      throw error;
    }
  }
  
  /**
   * Activate farmer account
   * @param farmerId Farmer ID
   * @returns Activated farmer
   */
  async activateAccount(farmerId: string): Promise<FarmerDocument | null> {
    try {
      // Check if farmer exists
      const farmer = await this.farmerRepository.findById(farmerId);
      if (!farmer) {
        throw new Error('Farmer not found');
      }

      // Check if farmer is verified
      if (farmer.verificationStatus !== 'VERIFIED') {
        throw new Error('Farmer must be verified before activation');
      }

      // Update status to active
      return await this.farmerRepository.update(farmerId, {
        status: 'ACTIVE',
      });
    } catch (error) {
      console.error('Error activating account:', error);
      throw error;
    }
  }

  /**
   * Perform background check on farmer
   * @param farmerId Farmer ID
   * @returns Background check result
   */
  async performBackgroundCheck(farmerId: string): Promise<{passed: boolean; details?: string}> {
    try {
      // Check if farmer exists
      const farmer = await this.farmerRepository.findById(farmerId);
      if (!farmer) {
        throw new Error('Farmer not found');
      }

      // In a real implementation, this would call an external service or API
      // For demo purposes, we'll simulate a successful background check

      // Simulate background check logic
      const passedCheck = Math.random() > 0.1; // 90% pass rate

      if (passedCheck) {
        return { passed: true };
      } else {
        return {
          passed: false,
          details: 'Failed background check due to insufficient documentation'
        };
      }
    } catch (error) {
      console.error('Error performing background check:', error);
      throw error;
    }
  }
} 
