import { FarmerLoginDto } from '../dto/farmer.dto';
import * as bcrypt from 'bcrypt';
import * as jwt from 'jsonwebtoken';
import {
  FarmerDocument,
  FarmerRepository,
  MongoDBConnector
} from '@libs/shared';

export class AuthService {
  private farmerRepository: FarmerRepository;
  private jwtSecret: string;

  constructor(dbConnector: MongoDBConnector) {
    this.farmerRepository = new FarmerRepository(dbConnector);
    this.jwtSecret = process.env.JWT_SECRET || 'befarma_';
  }
  
  /**
   * Login a seller
   * @param loginDto Login data
   * @returns JWT token and seller data
   */
  async login(loginDto: FarmerLoginDto): Promise<{ token: string; seller: Partial<FarmerDocument> }> {
    try {
      // Find farmer by email with password for authentication
      const seller = await this.farmerRepository.findByEmailWithPassword(loginDto.email);
      
      if (!seller) {
        throw new Error('Invalid email or password');
      }
      
      // Check password - now properly checking against bcrypted password in database
      if (!(seller as any).password) {
        throw new Error('Password not set for this account. Please contact support.');
      }

      const isPasswordValid = await bcrypt.compare(loginDto.password, (seller as any).password);
      if (!isPasswordValid) {
        throw new Error('Invalid email or password');
      }
      
      // Check if seller account is active
      if (seller.status !== 'ACTIVE') {
        throw new Error('Your account is not active. Please contact support.');
      }
      
      // Generate JWT token
      const token = this.generateToken(seller);
      
      // Return token and seller data (without sensitive info)
      return {
        token,
        seller: {
          farmerId: seller.farmerId,
          personalInfo: {
            name: seller.personalInfo.name,
            email: seller.personalInfo.email,
            contact: seller.personalInfo.contact,
            address: seller.personalInfo.address,
          },
          status: seller.status,
          verificationStatus: seller.verificationStatus,
        },
      };
    } catch (error) {
      console.error('Error in login:', error);
      throw error;
    }
  }
  
  /**
   * Generate JWT token
   * @param seller Seller document
   * @returns JWT token
   */
  private generateToken(seller: FarmerDocument): string {
    const payload = {
      id: seller.farmerId,
      email: seller.personalInfo.email,
      name: seller.personalInfo.name,
      role: 'farmer',
    };
    
    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: '1d', // Token expires in 1 day
    });
  }
  
  /**
   * Verify JWT token
   * @param token JWT token
   * @returns Decoded token payload
   */
  verifyToken(token: string): any {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      console.error('Error verifying token:', error);
      throw new Error('Invalid token');
    }
  }
} 