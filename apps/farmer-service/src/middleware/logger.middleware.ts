import { createRequestLogger, createErrorLogger } from '@libs/shared';

/**
 * Logger middleware
 * Logs HTTP requests to the console
 */
export const requestLogger = createRequestLogger({
  logLevel: (process.env.LOG_LEVEL || 'info') as 'debug' | 'info' | 'warn' | 'error',
  includeBody: process.env.NODE_ENV === 'development',
});

/**
 * Error logger middleware
 * Logs errors to the console
 */
export const errorLogger = createErrorLogger(); 