/**
 * Data Transfer Objects for Farmer Service
 */

/**
 * DTO for farmer registration
 */
export interface RegisterFarmerDto {
  name: string;
  email: string;
  contact: string;
  address: {
    country?: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2?: string;
  };
  password: string;
  // Optional fields
  documents?: {
    identityProof?: string;
    landOwnership?: string;
    certifications?: string[];
  };
  bankDetails?: {
    accountNumber: string;
    bankName: string;
    ifscCode: string;
  };
  farmingInfo?: {
    experience?: number;
    specializations?: string[];
    certifications?: {
      certId: string;
      type: string;
      issuer: string;
      issueDate: Date;
      expiryDate: Date;
    }[];
    training?: {
      trainingId: string;
      name: string;
      completionDate: Date;
      status: 'COMPLETED' | 'IN_PROGRESS' | 'PENDING';
    }[];
  };
}

/**
 * DTO for farmer verification documents
 */
export interface FarmerDocumentsDto {
  identityProof: string; // Document ID or file path
  landOwnership: string; // Document ID or file path
  certifications?: string[]; // Array of certification document IDs or file paths
}

/**
 * DTO for farmer bank details
 */
export interface FarmerBankDetailsDto {
  accountNumber: string;
  bankName: string;
  ifscCode: string;
}

/**
 * DTO for farmer profile update
 */
export interface UpdateFarmerProfileDto {
  name?: string;
  contact?: string;
  email?: string;
  address?: {
    country?: string;
    state?: string;
    city?: string;
    pincode?: string;
    addressLine1?: string;
    addressLine2?: string;
  };
}

/**
 * DTO for farmer verification status update
 */
export interface UpdateVerificationStatusDto {
  farmerId: string;
  status: 'PENDING' | 'VERIFIED' | 'REJECTED';
  reason?: string;
}

/**
 * DTO for farmer login
 */
export interface FarmerLoginDto {
  email: string;
  password: string;
}

// Backward compatibility exports
export type RegisterSellerDto = RegisterFarmerDto;
export type SellerDocumentsDto = FarmerDocumentsDto;
export type SellerBankDetailsDto = FarmerBankDetailsDto;
export type UpdateSellerProfileDto = UpdateFarmerProfileDto;
export type SellerLoginDto = FarmerLoginDto;
