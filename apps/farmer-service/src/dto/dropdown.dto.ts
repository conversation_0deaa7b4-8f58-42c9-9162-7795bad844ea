/**
 * DTOs for dropdown listing operations
 */

export interface FarmDropdownDto {
  farmId: string;
  name: string;
  status: 'ACTIVE' | 'INACTIVE';
  location?: {
    city: string;
    state: string;
  };
}

export interface CropDropdownDto {
  cropId: string;
  name: string;
  category: string;
  status: 'AVAILABLE' | 'SOLD' | 'RESERVED' | 'HARVESTING';
  farmName?: string;
}

export interface EnumDropdownDto {
  name: string;
  values: string[];
}

export interface DropdownQueryDto {
  sellerId?: string;
  status?: string;
  search?: string;
  limit?: number;
}

export interface DropdownResponseDto<T> {
  success: boolean;
  data: T[];
  total: number;
  message?: string;
}
