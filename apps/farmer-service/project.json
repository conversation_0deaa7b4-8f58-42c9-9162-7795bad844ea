{"name": "farmer-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/farmer-service/src", "projectType": "application", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/farmer-service", "format": ["cjs"], "bundle": true, "main": "apps/farmer-service/src/main.ts", "tsConfig": "apps/farmer-service/tsconfig.app.json", "assets": ["apps/farmer-service/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {"fileReplacements": [{"replace": "apps/farmer-service/.env", "with": "apps/farmer-service/.env.development"}]}, "production": {"fileReplacements": [{"replace": "apps/farmer-service/.env", "with": "apps/farmer-service/.env.production"}]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "farmer-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "farmer-service:build:development"}, "production": {"buildTarget": "farmer-service:build:production"}}}}, "tags": []}