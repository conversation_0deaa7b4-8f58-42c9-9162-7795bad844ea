# Farmer Service

## Overview
The Farmer Service handles all farmer-related operations in the AgriTech platform, including:
- Farmer registration and onboarding
- Document verification
- Background checks
- Account activation
- Profile management
- Bank account details management

## API Endpoints

### Authentication
- `POST /api/farmers/login` - Authenticate a farmer and get JWT token

### Farmer Onboarding
- `POST /api/farmers/register` - Register a new farmer
- `POST /api/farmers/documents/:farmerId` - Upload farmer documents
- `POST /api/farmers/verify` - Update farmer verification status
- `POST /api/farmers/activate/:farmerId` - Activate farmer account
- `GET /api/farmers/background-check/:farmerId` - Perform background check

### Farmer Profile
- `GET /api/farmers/profile` - Get farmer profile (uses JWT token)
- `PUT /api/farmers/profile` - Update farmer profile (uses JWT token)
- `PUT /api/farmers/profile/bank-details` - Update bank details (uses JWT token)
- `GET /api/farmers` - Get all farmers (with pagination)
- `DELETE /api/farmers/profile` - Delete farmer account (uses JWT token)

### KYC Management
- `POST /api/farmers/kyc/submit` - Submit KYC documents (uses JWT token)
- `GET /api/farmers/kyc/status` - Get KYC status (uses JWT token)
- `GET /api/farmers/dashboard` - Get farmer dashboard (uses JWT token)

### Farm Management
- `POST /api/farmers/farms` - Create farm (uses JWT token)
- `GET /api/farmers/farms` - Get farmer farms (uses JWT token)
- `GET /api/farmers/farms/:farmId` - Get farm by ID
- `PUT /api/farmers/farms/:farmId` - Update farm
- `DELETE /api/farmers/farms/:farmId` - Delete farm

## Environment Variables
Create a `.env` file in the root directory with the following variables:

```
# Server Configuration
PORT=3333
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/farmer_backend

# JWT Configuration
JWT_SECRET=your-secret-key-change-this-in-production
JWT_EXPIRES_IN=1d

# CORS Configuration
CORS_ORIGIN=*

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_REGISTER_MAX=3

# Logging
LOG_LEVEL=debug
```

## Getting Started

### Prerequisites
- Node.js (>= 16.x)
- MongoDB

### Installation
1. Install dependencies:
   ```
   npm install
   ```

2. Set up environment variables:
   - Create a `.env` file in the root directory
   - Add required environment variables (see above)

3. Run the service:
   ```
   npx nx serve farmer-service
   ```

4. For development with hot reloading:
   ```
   npx nx serve farmer-service --watch
   ```

## Project Structure
- `src/controllers` - API controllers (handle HTTP requests and responses)
- `src/services` - Business logic (independent of HTTP layer)
- `src/dto` - Data transfer objects (define data structures)
- `src/routes` - API routes (define endpoints)
- `src/middleware` - Middleware functions (authentication, validation, etc.)
- `src/config` - Configuration files (database, environment variables)

## Authentication
The service uses JWT (JSON Web Tokens) for authentication. To access protected endpoints:

1. Login using `/api/farmers/login`
2. Receive a JWT token
3. Include the token in the Authorization header of subsequent requests:
   ```
   Authorization: Bearer <your-token>
   ```

## Security Features
- JWT-based authentication
- Request validation
- Rate limiting
- CORS protection
- Helmet security headers
- Error handling middleware

## Dependencies
- Express - Web framework
- Mongoose - MongoDB ODM
- JWT - Authentication
- bcrypt - Password hashing
- dotenv - Environment variables
- cors - CORS middleware
- helmet - Security headers
- express-rate-limit - Rate limiting 