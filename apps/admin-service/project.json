{"name": "admin-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/admin-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/admin-service", "format": ["cjs"], "bundle": true, "main": "apps/admin-service/src/main.ts", "tsConfig": "apps/admin-service/tsconfig.app.json", "assets": ["apps/admin-service/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {"fileReplacements": [{"replace": "apps/admin-service/.env", "with": "apps/admin-service/.env.development"}]}, "docker": {"fileReplacements": [{"replace": "apps/admin-service/.env", "with": "apps/admin-service/.env.docker"}]}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "admin-service:build"}, "configurations": {"development": {"buildTarget": "admin-service:build:development"}, "docker": {"buildTarget": "admin-service:build:docker"}}}}}