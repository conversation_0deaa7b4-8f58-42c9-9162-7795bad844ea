import { Request, Response, NextFunction } from 'express';

// Request logger middleware
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const timestamp = new Date().toISOString();
  const { method, url, ip } = req;
  const userAgent = req.get('User-Agent') || 'Unknown';
  
  console.log(`[${timestamp}] ${method} ${url} - IP: ${ip} - User-Agent: ${userAgent}`);
  
  // Log request body for debugging (be careful with sensitive data)
  if (process.env.NODE_ENV === 'development' && req.body && Object.keys(req.body).length > 0) {
    console.log('Request Body:', JSON.stringify(req.body, null, 2));
  }
  
  next();
};

// Error logger middleware
export const errorLogger = (err: any, req: Request, res: Response, next: NextFunction): void => {
  const timestamp = new Date().toISOString();
  const { method, url, ip } = req;
  
  console.error(`[${timestamp}] ERROR - ${method} ${url} - IP: ${ip}`);
  console.error('Error details:', {
    name: err.name,
    message: err.message,
    stack: err.stack,
  });
  
  next(err);
};

// Response logger middleware
export const responseLogger = (req: Request, res: Response, next: NextFunction): void => {
  const originalSend = res.send;
  const startTime = Date.now();
  
  res.send = function(body) {
    const timestamp = new Date().toISOString();
    const duration = Date.now() - startTime;
    const { method, url } = req;
    const { statusCode } = res;
    
    console.log(`[${timestamp}] ${method} ${url} - ${statusCode} - ${duration}ms`);
    
    return originalSend.call(this, body);
  };
  
  next();
}; 