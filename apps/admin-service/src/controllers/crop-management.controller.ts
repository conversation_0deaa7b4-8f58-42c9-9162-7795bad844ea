import { Request, Response } from 'express';
import { dbManager } from '../../../../libs/shared/database-connectors/db-manager';
import { CropModel, ICrop } from '../../../../libs/shared/database-connectors/schemas/mongo/crop.schema';

/**
 * Controller for crop management operations in admin service
 */
export class CropManagementController {

  /**
   * Get all crops with pagination and filtering
   */
  getAllCrops = async (req: Request, res: Response): Promise<void> => {
    try {
      const { 
        page = 1, 
        limit = 10, 
        sellerId, 
        farmId, 
        type, 
        growthStage, 
        healthStatus 
      } = req.query;
      
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      // Build query filters
      const query: any = {};
      if (sellerId) query.sellerId = sellerId;
      if (farmId) query.farmId = farmId;
      if (type) query.type = type;
      if (growthStage) query.growthStage = growthStage;
      if (healthStatus) query['health.status'] = healthStatus;

      // Get crops with pagination
      const crops = await CropModel.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .lean();

      const total = await CropModel.countDocuments(query);
      const totalPages = Math.ceil(total / limitNum);

      res.status(200).json({
        success: true,
        data: {
          crops,
          pagination: {
            total,
            page: pageNum,
            limit: limitNum,
            totalPages
          }
        }
      });
    } catch (error) {
      console.error('Error getting crops:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get crops'
        }
      });
    }
  };

  /**
   * Get crop by ID
   */
  getCropById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { cropId } = req.params;

      const crop = await CropModel.findOne({ cropId }).lean();

      if (!crop) {
        res.status(404).json({
          success: false,
          error: {
            code: 'CROP_NOT_FOUND',
            message: 'Crop not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: crop
      });
    } catch (error) {
      console.error('Error getting crop:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to get crop'
        }
      });
    }
  };

  /**
   * Update crop details
   */
  updateCrop = async (req: Request, res: Response): Promise<void> => {
    try {
      const { cropId } = req.params;
      const updateData = req.body;

      // Remove fields that shouldn't be updated directly
      delete updateData.cropId;
      delete updateData.createdAt;
      delete updateData._id;

      const updatedCrop = await CropModel.findOneAndUpdate(
        { cropId },
        { $set: { ...updateData, updatedAt: new Date() } },
        { new: true, lean: true }
      );

      if (!updatedCrop) {
        res.status(404).json({
          success: false,
          error: {
            code: 'CROP_NOT_FOUND',
            message: 'Crop not found'
          }
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          message: 'Crop updated successfully',
          crop: updatedCrop
        }
      });
    } catch (error) {
      console.error('Error updating crop:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update crop'
        }
      });
    }
  };

  /**
   * Verify/approve crop
   */
  verifyCrop = async (req: Request, res: Response): Promise<void> => {
    try {
      const { cropId } = req.params;
      const { approved, notes, adminId } = req.body;

      const crop = await CropModel.findOne({ cropId });

      if (!crop) {
        res.status(404).json({
          success: false,
          error: {
            code: 'CROP_NOT_FOUND',
            message: 'Crop not found'
          }
        });
        return;
      }

      // Add verification status to metadata
      const verificationData = {
        'metadata.verificationStatus': approved ? 'VERIFIED' : 'REJECTED',
        'metadata.verifiedBy': adminId || 'ADMIN',
        'metadata.verificationDate': new Date(),
        'metadata.verificationNotes': notes || `Crop ${approved ? 'verified' : 'rejected'} by admin`,
        updatedAt: new Date()
      };

      const updatedCrop = await CropModel.findOneAndUpdate(
        { cropId },
        { $set: verificationData },
        { new: true, lean: true }
      );

      res.status(200).json({
        success: true,
        data: {
          message: `Crop ${approved ? 'verified' : 'rejected'} successfully`,
          crop: updatedCrop,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error verifying crop:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to verify crop'
        }
      });
    }
  };

  /**
   * Delete crop
   */
  deleteCrop = async (req: Request, res: Response): Promise<void> => {
    try {
      const { cropId } = req.params;
      const { reason, adminId } = req.body;

      const crop = await CropModel.findOne({ cropId });

      if (!crop) {
        res.status(404).json({
          success: false,
          error: {
            code: 'CROP_NOT_FOUND',
            message: 'Crop not found'
          }
        });
        return;
      }

      // Soft delete by marking as deleted
      const deletedCrop = await CropModel.findOneAndUpdate(
        { cropId },
        { 
          $set: { 
            'metadata.deleted': true,
            'metadata.deletedBy': adminId || 'ADMIN',
            'metadata.deletedAt': new Date(),
            'metadata.deletionReason': reason || 'Deleted by admin',
            updatedAt: new Date()
          }
        },
        { new: true, lean: true }
      );

      res.status(200).json({
        success: true,
        data: {
          message: 'Crop deleted successfully',
          cropId,
          reason,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error deleting crop:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to delete crop'
        }
      });
    }
  };
}
