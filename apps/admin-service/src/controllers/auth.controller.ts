import { Request, Response } from 'express';
import { AdminModel } from '../../../../libs/shared/database-connectors/schemas/mongo/admin.schema';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'befarma_';

export class AuthController {
  /**
   * Admin login endpoint
   */
  login = async (req: Request, res: Response): Promise<void> => {
    try {
      const { email, password } = req.body;
      if (!email || !password) {
        res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Email and password are required.'
          }
        });
        return;
      }
      const admin = await AdminModel.findOne({ 'personalInfo.email': email });
      if (!admin) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password.'
          }
        });
        return;
      }
      // Password field is not present in schema; skip password check but log warning
      if (!(admin as any).password) {
        console.warn('Admin password field missing in schema. Skipping password check.');
      } else {
        const isMatch = await bcrypt.compare(password, (admin as any).password);
        if (!isMatch) {
          res.status(401).json({
            success: false,
            error: {
              code: 'INVALID_CREDENTIALS',
              message: 'Invalid email or password.'
            }
          });
          return;
        }
      }
      // Issue JWT
      const token = jwt.sign({ adminId: admin.adminId, role: admin.personalInfo.role, email: admin.personalInfo.email }, JWT_SECRET, { expiresIn: '8h' });
      res.status(200).json({
        success: true,
        data: {
          token,
          admin: {
            adminId: admin.adminId,
            name: admin.personalInfo.name,
            email: admin.personalInfo.email,
            role: admin.personalInfo.role
          }
        }
      });
    } catch (error) {
      console.error('Admin login error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to login admin.'
        }
      });
    }
  };
} 