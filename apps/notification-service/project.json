{"name": "notification-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/notification-service/src", "projectType": "application", "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"platform": "node", "outputPath": "dist/notification-service", "format": ["cjs"], "bundle": true, "main": "apps/notification-service/src/main.ts", "tsConfig": "apps/notification-service/tsconfig.app.json", "assets": ["apps/notification-service/src/assets"], "generatePackageJson": true, "esbuildOptions": {"sourcemap": true, "outExtension": {".js": ".js"}}}, "configurations": {"development": {"fileReplacements": [{"replace": "apps/notification-service/.env", "with": "apps/notification-service/.env.development"}]}, "production": {"fileReplacements": [{"replace": "apps/notification-service/.env", "with": "apps/notification-service/.env.production"}]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "notification-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "notification-service:build:development"}, "production": {"buildTarget": "notification-service:build:production"}}}}, "tags": []}