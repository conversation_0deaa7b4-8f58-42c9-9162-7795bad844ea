
import express from 'express';
import * as path from 'path';
import mongoose from 'mongoose';

const app = express();

app.use('/assets', express.static(path.join(__dirname, 'assets')));
app.get('/api', (req, res) => {
  res.send({ message: 'Welcome to notification-service!' });
});

const port = process.env.PORT || 3333;
const server = app.listen(port, () => {
  console.log(`Listening at http://localhost:${port}/api`);
});
server.on('error', console.error);
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'UP',
    service: '${SERVICE}',
    database: mongoose.connection ? (mongoose.connection.readyState === 1 ? 'connected' : 'disconnected') : 'not applicable',
    timestamp: new Date().toISOString()
  });
});
