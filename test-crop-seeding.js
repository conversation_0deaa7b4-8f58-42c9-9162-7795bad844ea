#!/usr/bin/env node

/**
 * Test script to verify crop seeding to both MongoDB and ElasticDB
 */

const { execSync } = require('child_process');

async function testCropSeeding() {
  try {
    console.log('🌱 Testing crop seeding to MongoDB and ElasticDB...\n');
    
    // Set environment variables for testing
    process.env.MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/agritech';
    process.env.ELASTICSEARCH_NODE = process.env.ELASTICSEARCH_NODE || 'https://befarma-f44523.es.us-east-1.aws.elastic.cloud:443';
    process.env.ELASTICSEARCH_INDEX = process.env.ELASTICSEARCH_INDEX || 'befarma';
    
    console.log('Environment variables:');
    console.log('- MONGODB_URI:', process.env.MONGODB_URI);
    console.log('- ELASTICSEARCH_NODE:', process.env.ELASTICSEARCH_NODE);
    console.log('- ELA<PERSON>ICSEARCH_INDEX:', process.env.ELASTICSEARCH_INDEX);
    console.log('- ELASTICSEARCH_API_KEY:', process.env.ELASTICSEARCH_API_KEY ? '[SET]' : '[NOT SET]');
    console.log('');
    
    // Test the seeding functionality
    console.log('📦 Running seed script...');
    
    try {
      const output = execSync('node -r ts-node/register libs/shared/database-connectors/scripts/seed-db.ts', {
        encoding: 'utf8',
        stdio: 'pipe',
        timeout: 60000 // 60 seconds timeout
      });
      
      console.log('✅ Seed script output:');
      console.log(output);
      
    } catch (error) {
      console.log('❌ Seed script failed:');
      console.log('Error code:', error.status);
      console.log('Error output:', error.stderr);
      console.log('Standard output:', error.stdout);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testCropSeeding();
