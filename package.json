{"name": "seller_backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "npx nx run-many --target=serve --all", "serve:all": "npx nx run-many --target=serve --all", "serve:crop": "npx nx serve crop-service", "serve:farm": "npx nx serve farm-service", "serve:admin": "npx nx serve admin-service", "build": "npx nx run-many --target=build --all", "test:connection": "node test-elasticsearch-connection.js", "setup:elasticsearch": "./setup-elasticsearch.sh"}, "repository": {"type": "git", "url": "git@bitbucket-in:haneef_shaik/seller_backend.git"}, "author": "", "license": "ISC", "description": "", "devDependencies": {"@nx/esbuild": "^21.1.1", "@nx/express": "^21.0.3", "@nx/js": "21.0.3", "@nx/node": "21.0.3", "@nx/web": "21.0.3", "@nx/webpack": "21.0.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.7", "@svgr/webpack": "^8.0.1", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^4.17.21", "@types/express-http-proxy": "^1.6.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "~18.16.9", "@types/uuid": "^9.0.8", "esbuild": "^0.25.4", "nx": "21.0.3", "prettier": "^2.6.2", "react-refresh": "^0.10.0", "tslib": "^2.3.0", "typescript": "~5.7.2", "webpack-cli": "^5.1.4"}, "nx": {}, "dependencies": {"@elastic/elasticsearch": "^8.18.2", "@types/multer": "^1.4.13", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "axios": "^1.6.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-http-proxy": "^2.1.1", "express-rate-limit": "^7.5.0", "helmet": "^7.2.0", "http-proxy-middleware": "^3.0.5", "http-status": "^2.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "multer": "^2.0.1", "uuid": "^9.0.1"}}