{"$schema": "./node_modules/nx/schemas/nx-schema.json", "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "test"]}}}, "plugins": [{"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview"}}], "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"]}, "serve": {"dependsOn": ["build"]}}, "namedInputs": {"production": ["{projectRoot}/src/**/*"]}}