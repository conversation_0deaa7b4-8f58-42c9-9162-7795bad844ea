{"name": "auth", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/feature/auth/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/feature/auth", "main": "libs/feature/auth/src/index.ts", "tsConfig": "libs/feature/auth/tsconfig.lib.json", "assets": ["libs/feature/auth/*.md"]}}}, "tags": []}