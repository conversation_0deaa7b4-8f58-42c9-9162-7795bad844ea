// Export axios configuration and clients
export * from './axios-config';

// Export API services
export * from './api-services';

// Re-export commonly used types from axios
export type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

/**
 * Convenience exports for easy access to API services
 */
import {
  AuthApiService,
  SellerApiService,
  FarmApiService,
  CropApiService,
  AdminApiService,
  AnalyticsApiService,
  NotificationApiService,
  OrderApiService,
} from './api-services';

// Create singleton instances of API services
export const authApi = new AuthApiService();
export const sellerApi = new SellerApiService();
export const farmApi = new FarmApiService();
export const cropApi = new CropApiService();
export const adminApi = new AdminApiService();
export const analyticsApi = new AnalyticsApiService();
export const notificationApi = new NotificationApiService();
export const orderApi = new OrderApiService();

/**
 * API service collection for easy access
 */
export const apiServices = {
  auth: authApi,
  seller: sellerApi,
  farm: farmApi,
  crop: cropApi,
  admin: adminApi,
  analytics: analyticsApi,
  notification: notificationApi,
  order: orderApi,
};

/**
 * Utility functions for common API operations
 */
export const apiUtils = {
  /**
   * Set authentication token for all services
   */
  setAuthToken: (token: string, service?: string) => {
    const { ApiClients } = require('./axios-config');
    ApiClients.setAuthToken(token, service);
  },

  /**
   * Clear all authentication tokens
   */
  clearAuthTokens: () => {
    const { ApiClients } = require('./axios-config');
    ApiClients.clearAuthTokens();
  },

  /**
   * Login and set token automatically
   */
  login: async (email: string, password: string) => {
    try {
      const result = await authApi.login(email, password);
      return result;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Admin login and set token automatically
   */
  adminLogin: async (email: string, password: string) => {
    try {
      const result = await adminApi.login(email, password);
      return result;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Logout and clear tokens
   */
  logout: async () => {
    try {
      await authApi.logout();
    } catch (error) {
      // Even if logout fails, clear tokens locally
      apiUtils.clearAuthTokens();
      throw error;
    }
  },

  /**
   * Check if user is authenticated
   */
  isAuthenticated: () => {
    const { TokenManager } = require('./axios-config');
    const tokenManager = TokenManager.getInstance();
    return !!tokenManager.getToken('default');
  },

  /**
   * Get current auth token
   */
  getAuthToken: (service?: string) => {
    const { TokenManager } = require('./axios-config');
    const tokenManager = TokenManager.getInstance();
    return tokenManager.getToken(service || 'default');
  },
};

/**
 * Type definitions for common API responses
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  errors?: any[];
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface AuthResponse {
  token: string;
  refreshToken?: string;
  user: {
    id: string;
    email: string;
    name: string;
    role: string;
    [key: string]: any;
  };
}

export interface AdminAuthResponse {
  token: string;
  refreshToken?: string;
  admin: {
    id: string;
    email: string;
    name: string;
    role: string;
    permissions: string[];
    [key: string]: any;
  };
}

/**
 * Common API error types
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ApiErrorResponse {
  success: false;
  message: string;
  errors?: ValidationError[];
  code?: string;
  statusCode: number;
}

/**
 * Request/Response interceptor types
 */
export interface RequestInterceptor {
  onFulfilled?: (config: any) => any;
  onRejected?: (error: any) => any;
}

export interface ResponseInterceptor {
  onFulfilled?: (response: any) => any;
  onRejected?: (error: any) => any;
}

/**
 * Configuration for API clients
 */
export interface ApiClientOptions {
  baseURL?: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  enableAuth?: boolean;
  enableLogging?: boolean;
  requestInterceptors?: RequestInterceptor[];
  responseInterceptors?: ResponseInterceptor[];
}

/**
 * Health check response type
 */
export interface HealthCheckResponse {
  status: 'UP' | 'DOWN' | 'DEGRADED';
  service: string;
  version?: string;
  timestamp: string;
  database?: 'connected' | 'disconnected' | 'not applicable';
  dependencies?: {
    [key: string]: 'UP' | 'DOWN';
  };
}

/**
 * Search parameters interface
 */
export interface SearchParams {
  q?: string;
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  filters?: {
    [key: string]: any;
  };
}

/**
 * File upload interface
 */
export interface FileUpload {
  file: File | Buffer;
  filename: string;
  mimetype: string;
  size: number;
}

/**
 * Bulk operation response
 */
export interface BulkOperationResponse {
  success: number;
  failed: number;
  total: number;
  errors?: Array<{
    index: number;
    error: string;
  }>;
}

/**
 * Export default API client configuration
 */
export const defaultApiConfig = {
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
  enableAuth: true,
  enableLogging: process.env['NODE_ENV'] === 'development',
};
