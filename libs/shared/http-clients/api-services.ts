import { AxiosInstance, AxiosResponse } from 'axios';
import { ApiClients } from './axios-config';

/**
 * Base API service class with common functionality
 */
export abstract class BaseApiService {
  protected client: AxiosInstance;

  constructor(client: AxiosInstance) {
    this.client = client;
  }

  /**
   * Handle API response and extract data
   */
  protected handleResponse<T>(response: AxiosResponse<T>): T {
    return response.data;
  }

  /**
   * Handle API errors
   */
  protected handleError(error: any): never {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      throw new ApiError(status, data.message || 'API Error', data);
    } else if (error.request) {
      // Network error
      throw new ApiError(0, 'Network Error', { message: 'Unable to connect to server' });
    } else {
      // Other error
      throw new ApiError(0, error.message || 'Unknown Error', error);
    }
  }
}

/**
 * Custom API Error class
 */
export class ApiError extends Error {
  public status: number;
  public data: any;

  constructor(status: number, message: string, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

/**
 * Authentication API Service
 */
export class AuthApiService extends BaseApiService {
  constructor() {
    super(ApiClients.getApiGateway());
  }

  async login(email: string, password: string): Promise<{ token: string; user: any }> {
    try {
      const response = await this.client.post('/auth/login', { email, password });
      const data = this.handleResponse(response);
      
      // Set token for future requests
      if (data.token) {
        ApiClients.setAuthToken(data.token);
      }
      
      return data;
    } catch (error) {
      this.handleError(error);
    }
  }

  async register(userData: any): Promise<{ token: string; user: any }> {
    try {
      const response = await this.client.post('/auth/register', userData);
      const data = this.handleResponse(response);
      
      // Set token for future requests
      if (data.token) {
        ApiClients.setAuthToken(data.token);
      }
      
      return data;
    } catch (error) {
      this.handleError(error);
    }
  }

  async refreshToken(refreshToken: string): Promise<{ token: string }> {
    try {
      const response = await this.client.post('/auth/refresh-token', { refreshToken });
      const data = this.handleResponse(response);
      
      // Update token
      if (data.token) {
        ApiClients.setAuthToken(data.token);
      }
      
      return data;
    } catch (error) {
      this.handleError(error);
    }
  }

  async logout(): Promise<void> {
    try {
      await this.client.post('/auth/logout');
      ApiClients.clearAuthTokens();
    } catch (error) {
      this.handleError(error);
    }
  }
}

/**
 * Seller API Service
 */
export class SellerApiService extends BaseApiService {
  constructor() {
    super(ApiClients.getSellerService());
  }

  async getAllSellers(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getSellerById(sellerId: string): Promise<any> {
    try {
      const response = await this.client.get(`/${sellerId}`);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async createSeller(sellerData: any): Promise<any> {
    try {
      const response = await this.client.post('/', sellerData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateSeller(sellerId: string, sellerData: any): Promise<any> {
    try {
      const response = await this.client.put(`/${sellerId}`, sellerData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async deleteSeller(sellerId: string): Promise<void> {
    try {
      await this.client.delete(`/${sellerId}`);
    } catch (error) {
      this.handleError(error);
    }
  }

  async uploadDocument(sellerId: string, documentData: FormData): Promise<any> {
    try {
      const response = await this.client.post(`/${sellerId}/documents`, documentData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async verifySeller(sellerId: string): Promise<any> {
    try {
      const response = await this.client.put(`/${sellerId}/verify`);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }
}

/**
 * Farm API Service
 */
export class FarmApiService extends BaseApiService {
  constructor() {
    super(ApiClients.getFarmService());
  }

  async getAllFarms(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getFarmById(farmId: string): Promise<any> {
    try {
      const response = await this.client.get(`/${farmId}`);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async createFarm(farmData: any): Promise<any> {
    try {
      const response = await this.client.post('/', farmData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateFarm(farmId: string, farmData: any): Promise<any> {
    try {
      const response = await this.client.put(`/${farmId}`, farmData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async deleteFarm(farmId: string): Promise<void> {
    try {
      await this.client.delete(`/${farmId}`);
    } catch (error) {
      this.handleError(error);
    }
  }

  async searchFarms(searchParams: any): Promise<any> {
    try {
      const response = await this.client.get('/search', { params: searchParams });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }
}

/**
 * Crop API Service
 */
export class CropApiService extends BaseApiService {
  constructor() {
    super(ApiClients.getCropService());
  }

  async getAllCrops(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getCropById(cropId: string): Promise<any> {
    try {
      const response = await this.client.get(`/${cropId}`);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async createCrop(cropData: any): Promise<any> {
    try {
      const response = await this.client.post('/', cropData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateCrop(cropId: string, cropData: any): Promise<any> {
    try {
      const response = await this.client.put(`/${cropId}`, cropData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async deleteCrop(cropId: string): Promise<void> {
    try {
      await this.client.delete(`/${cropId}`);
    } catch (error) {
      this.handleError(error);
    }
  }

  async searchCrops(searchParams: any): Promise<any> {
    try {
      const response = await this.client.get('/search', { params: searchParams });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getCropSuggestions(query: string): Promise<any> {
    try {
      const response = await this.client.get('/suggestions', { params: { q: query } });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }
}

/**
 * Admin API Service
 */
export class AdminApiService extends BaseApiService {
  constructor() {
    super(ApiClients.getAdminService());
  }

  async login(email: string, password: string): Promise<{ token: string; admin: any }> {
    try {
      const response = await this.client.post('/login', { email, password });
      const data = this.handleResponse(response);
      
      // Set admin token
      if (data.token) {
        ApiClients.setAuthToken(data.token, 'admin');
      }
      
      return data;
    } catch (error) {
      this.handleError(error);
    }
  }

  async getDashboard(): Promise<any> {
    try {
      const response = await this.client.get('/dashboard');
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getSystemHealth(): Promise<any> {
    try {
      const response = await this.client.get('/system/health');
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async manageSellers(action: string, sellerId?: string, data?: any): Promise<any> {
    try {
      let response;
      switch (action) {
        case 'list':
          response = await this.client.get('/sellers');
          break;
        case 'get':
          response = await this.client.get(`/sellers/${sellerId}`);
          break;
        case 'verify':
          response = await this.client.put(`/sellers/${sellerId}/verify`, data);
          break;
        case 'update':
          response = await this.client.put(`/sellers/${sellerId}`, data);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }
}

/**
 * Analytics API Service
 */
export class AnalyticsApiService extends BaseApiService {
  constructor() {
    super(ApiClients.getAnalyticsService());
  }

  async getDashboard(): Promise<any> {
    try {
      const response = await this.client.get('/dashboard');
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getSalesAnalytics(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/sales', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getCropAnalytics(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/crops', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getUserAnalytics(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/users', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async generateReport(reportConfig: any): Promise<any> {
    try {
      const response = await this.client.post('/reports/generate', reportConfig);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getReportStatus(reportId: string): Promise<any> {
    try {
      const response = await this.client.get(`/reports/${reportId}`);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getMarketInsights(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/market/insights', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async exportData(exportConfig: any): Promise<any> {
    try {
      const response = await this.client.post('/export', exportConfig);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }
}

/**
 * Notification API Service
 */
export class NotificationApiService extends BaseApiService {
  constructor() {
    super(ApiClients.getNotificationService());
  }

  async getNotifications(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async sendNotification(notificationData: any): Promise<any> {
    try {
      const response = await this.client.post('/', notificationData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async markAsRead(notificationId: string): Promise<any> {
    try {
      const response = await this.client.put(`/${notificationId}/read`);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async markAllAsRead(): Promise<any> {
    try {
      const response = await this.client.put('/read-all');
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async deleteNotification(notificationId: string): Promise<void> {
    try {
      await this.client.delete(`/${notificationId}`);
    } catch (error) {
      this.handleError(error);
    }
  }

  async sendBulkNotifications(notificationsData: any): Promise<any> {
    try {
      const response = await this.client.post('/bulk', notificationsData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getTemplates(): Promise<any> {
    try {
      const response = await this.client.get('/templates');
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async createTemplate(templateData: any): Promise<any> {
    try {
      const response = await this.client.post('/templates', templateData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getPreferences(): Promise<any> {
    try {
      const response = await this.client.get('/preferences');
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async updatePreferences(preferencesData: any): Promise<any> {
    try {
      const response = await this.client.put('/preferences', preferencesData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }
}

/**
 * Order API Service
 */
export class OrderApiService extends BaseApiService {
  constructor() {
    super(ApiClients.getOrderService());
  }

  async getAllOrders(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getOrderById(orderId: string): Promise<any> {
    try {
      const response = await this.client.get(`/${orderId}`);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async createOrder(orderData: any): Promise<any> {
    try {
      const response = await this.client.post('/', orderData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async updateOrder(orderId: string, orderData: any): Promise<any> {
    try {
      const response = await this.client.put(`/${orderId}`, orderData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async cancelOrder(orderId: string, reason?: string): Promise<any> {
    try {
      const response = await this.client.put(`/${orderId}/cancel`, { reason });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getOrderTracking(orderId: string): Promise<any> {
    try {
      const response = await this.client.get(`/${orderId}/tracking`);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async processPayment(orderId: string, paymentData: any): Promise<any> {
    try {
      const response = await this.client.post(`/${orderId}/payment`, paymentData);
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }

  async getOrderAnalytics(params?: any): Promise<any> {
    try {
      const response = await this.client.get('/analytics', { params });
      return this.handleResponse(response);
    } catch (error) {
      this.handleError(error);
    }
  }
}
