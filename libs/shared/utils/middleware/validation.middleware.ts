import { Request, Response, NextFunction } from 'express';
import { createError } from './auth.middleware';

/**
 * Interface for validation schema
 */
export interface ValidationSchema {
  [key: string]: {
    required?: boolean;
    type?: 'string' | 'number' | 'boolean' | 'object' | 'array';
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    validate?: (value: any) => boolean | { valid: boolean; message: string };
  };
}

/**
 * Create a body validation middleware
 * @param schema Validation schema
 * @returns Middleware function
 */
export const createBodyValidator = (schema: ValidationSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const errors: string[] = [];
      
      // Loop through schema fields
      for (const [field, rules] of Object.entries(schema)) {
        const value = req.body[field];
        
        // Check required
        if (rules.required && (value === undefined || value === null || value === '')) {
          errors.push(`${field} is required`);
          continue;
        }
        
        // Skip validation if value is undefined/null and field is not required
        if (value === undefined || value === null) {
          continue;
        }
        
        // Check type
        if (rules.type) {
          const actualType = Array.isArray(value) ? 'array' : typeof value;
          if (actualType !== rules.type) {
            errors.push(`${field} must be of type ${rules.type}`);
          }
        }
        
        // Validate string constraints
        if (typeof value === 'string') {
          if (rules.minLength && value.length < rules.minLength) {
            errors.push(`${field} must be at least ${rules.minLength} characters`);
          }
          
          if (rules.maxLength && value.length > rules.maxLength) {
            errors.push(`${field} must be at most ${rules.maxLength} characters`);
          }
          
          if (rules.pattern && !rules.pattern.test(value)) {
            errors.push(`${field} has an invalid format`);
          }
        }
        
        // Validate number constraints
        if (typeof value === 'number') {
          if (rules.min !== undefined && value < rules.min) {
            errors.push(`${field} must be at least ${rules.min}`);
          }
          
          if (rules.max !== undefined && value > rules.max) {
            errors.push(`${field} must be at most ${rules.max}`);
          }
        }
        
        // Custom validation
        if (rules.validate && value !== undefined) {
          const result = rules.validate(value);
          
          if (typeof result === 'boolean') {
            if (!result) {
              errors.push(`${field} is invalid`);
            }
          } else if (!result.valid) {
            errors.push(result.message);
          }
        }
      }
      
      if (errors.length > 0) {
        throw createError(errors.join('. '), 400);
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Create a params validation middleware
 * @param schema Validation schema
 * @returns Middleware function
 */
export const createParamsValidator = (schema: ValidationSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const errors: string[] = [];
      
      // Loop through schema fields
      for (const [field, rules] of Object.entries(schema)) {
        const value = req.params[field];
        
        // Check required
        if (rules.required && (value === undefined || value === null || value === '')) {
          errors.push(`${field} parameter is required`);
          continue;
        }
        
        // Skip validation if value is undefined/null and field is not required
        if (value === undefined || value === null) {
          continue;
        }
        
        // Most params will be strings, but we can enforce other validation
        if (rules.minLength && value.length < rules.minLength) {
          errors.push(`${field} parameter must be at least ${rules.minLength} characters`);
        }
        
        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push(`${field} parameter must be at most ${rules.maxLength} characters`);
        }
        
        if (rules.pattern && !rules.pattern.test(value)) {
          errors.push(`${field} parameter has an invalid format`);
        }
        
        // Custom validation
        if (rules.validate && value !== undefined) {
          const result = rules.validate(value);
          
          if (typeof result === 'boolean') {
            if (!result) {
              errors.push(`${field} parameter is invalid`);
            }
          } else if (!result.valid) {
            errors.push(result.message);
          }
        }
      }
      
      if (errors.length > 0) {
        throw createError(errors.join('. '), 400);
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Create a query validation middleware
 * @param schema Validation schema
 * @returns Middleware function
 */
export const createQueryValidator = (schema: ValidationSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const errors: string[] = [];
      
      // Loop through schema fields
      for (const [field, rules] of Object.entries(schema)) {
        const value = req.query[field] as string;
        
        // Check required
        if (rules.required && (value === undefined || value === null || value === '')) {
          errors.push(`${field} query parameter is required`);
          continue;
        }
        
        // Skip validation if value is undefined/null and field is not required
        if (value === undefined || value === null) {
          continue;
        }
        
        // Validate string constraints
        if (typeof value === 'string') {
          if (rules.minLength && value.length < rules.minLength) {
            errors.push(`${field} query parameter must be at least ${rules.minLength} characters`);
          }
          
          if (rules.maxLength && value.length > rules.maxLength) {
            errors.push(`${field} query parameter must be at most ${rules.maxLength} characters`);
          }
          
          if (rules.pattern && !rules.pattern.test(value)) {
            errors.push(`${field} query parameter has an invalid format`);
          }
        }
        
        // Custom validation
        if (rules.validate && value !== undefined) {
          const result = rules.validate(value);
          
          if (typeof result === 'boolean') {
            if (!result) {
              errors.push(`${field} query parameter is invalid`);
            }
          } else if (!result.valid) {
            errors.push(result.message);
          }
        }
      }
      
      if (errors.length > 0) {
        throw createError(errors.join('. '), 400);
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
}; 