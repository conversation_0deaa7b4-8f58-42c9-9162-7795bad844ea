import rateLimit from 'express-rate-limit';
import { createError } from './auth.middleware';

/**
 * Configuration for rate limiter
 */
export interface RateLimiterConfig {
  windowMs?: number;
  max?: number;
  message?: string;
  standardHeaders?: boolean;
  legacyHeaders?: boolean;
  skipSuccessfulRequests?: boolean;
}

/**
 * Create a rate limiter middleware
 * @param config Rate limiter configuration
 * @returns Rate limiter middleware
 */
export const createRateLimiter = (config: RateLimiterConfig = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // Limit each IP to 100 requests per windowMs
    message = 'Too many requests from this IP, please try again later',
    standardHeaders = true,
    legacyHeaders = false,
    skipSuccessfulRequests = false,
  } = config;
  
  return rateLimit({
    windowMs,
    max,
    message,
    standardHeaders,
    legacyHeaders,
    skipSuccessfulRequests,
    handler: (req, res, next, options) => {
      next(createError(options.message, 429));
    },
  });
};

/**
 * Create a standard rate limiter for API endpoints
 * Allows 100 requests per IP per 15 minutes
 * @returns Rate limiter middleware
 */
export const createStandardLimiter = () => {
  return createRateLimiter({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again after 15 minutes',
  });
};

/**
 * Create an authentication rate limiter
 * Allows 5 requests per IP per 15 minutes
 * This helps prevent brute force attacks
 * @returns Rate limiter middleware
 */
export const createAuthLimiter = () => {
  return createRateLimiter({
    windowMs: 15 * 60 * 1000,
    max: 5,
    message: 'Too many login attempts from this IP, please try again after 15 minutes',
  });
};

/**
 * Create a registration rate limiter
 * Allows 3 registrations per IP per day
 * This helps prevent automated registrations
 * @returns Rate limiter middleware
 */
export const createRegistrationLimiter = () => {
  return createRateLimiter({
    windowMs: 24 * 60 * 60 * 1000, // 24 hours
    max: 3,
    message: 'Too many registration attempts from this IP, please try again after 24 hours',
  });
}; 