import { Request, Response, NextFunction } from 'express';
import { AppError } from './auth.middleware';

/**
 * Configuration for error handler middleware
 */
export interface ErrorHandlerConfig {
  showStackTrace?: boolean;
  defaultMessage?: string;
}

/**
 * Creates an error handler middleware
 * @param config Error handler configuration
 * @returns Error handler middleware function
 */
export const createErrorHandler = (config: ErrorHandlerConfig = {}) => {
  const { 
    showStackTrace = process.env['NODE_ENV'] === 'development',
    defaultMessage = 'Something went wrong'
  } = config;
  
  return (
    err: AppError,
    req: Request,
    res: Response,
    next: NextFunction
  ): void => {
    err.statusCode = err.statusCode || 500;
    err.status = err.status || 'error';

    // Development error response (more details)
    if (showStackTrace) {
      res.status(err.statusCode).json({
        status: err.status,
        message: err.message,
        stack: err.stack,
        error: err,
      });
    } 
    // Production error response (less details)
    else {
      // Operational errors: trusted errors that we can send to the client
      if (err.isOperational) {
        res.status(err.statusCode).json({
          status: err.status,
          message: err.message,
        });
      } 
      // Programming errors: don't leak error details
      else {
        console.error('ERROR 💥', err);
        res.status(500).json({
          status: 'error',
          message: defaultMessage,
        });
      }
    }
  };
}; 