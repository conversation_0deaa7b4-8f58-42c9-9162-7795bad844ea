import { Request, Response, NextFunction } from 'express';

/**
 * Configuration for logger middleware
 */
export interface LoggerConfig {
  useColors?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  includeBody?: boolean;
  includeHeaders?: boolean;
  sensitiveFields?: string[];
}

/**
 * Default logger configuration
 */
const defaultConfig: LoggerConfig = {
  useColors: true,
  logLevel: 'info',
  includeBody: false,
  includeHeaders: false,
  sensitiveFields: ['password', 'token', 'secret', 'credit_card', 'authorization'],
};

/**
 * Creates a logger middleware
 * @param config Logger configuration
 * @returns Logger middleware function
 */
export const createRequestLogger = (customConfig: Partial<LoggerConfig> = {}) => {
  const config = { ...defaultConfig, ...customConfig };
  
  return (req: Request, res: Response, next: NextFunction): void => {
    const start = Date.now();
    
    // Log once the response is sent
    res.on('finish', () => {
      const duration = Date.now() - start;
      const { method, originalUrl, ip } = req;
      const { statusCode } = res;
      
      // Base log message
      let logMessage = `${new Date().toISOString()} | ${method} ${originalUrl} | ${statusCode} | ${duration}ms | ${ip}`;
      
      // Add request headers if configured
      if (config.includeHeaders) {
        logMessage += ` | Headers: ${sanitizeData(req.headers, config.sensitiveFields)}`;
      }
      
      // Add request body if configured
      if (config.includeBody && req.body) {
        logMessage += ` | Body: ${sanitizeData(req.body, config.sensitiveFields)}`;
      }
      
      // Use different log levels based on status code
      if (statusCode >= 500) {
        console.error(logMessage);
      } else if (statusCode >= 400) {
        console.warn(logMessage);
      } else if (config.logLevel === 'debug') {
        console.debug(logMessage);
      } else {
        console.log(logMessage);
      }
    });
    
    next();
  };
};

/**
 * Creates an error logger middleware
 * @returns Error logger middleware function
 */
export const createErrorLogger = () => {
  return (error: Error, req: Request, res: Response, next: NextFunction): void => {
    console.error(`${new Date().toISOString()} | ERROR | ${req.method} ${req.originalUrl} | ${error.message}`);
    console.error(error.stack);
    
    next(error);
  };
};

/**
 * Sanitizes sensitive information from objects
 * @param data Data to sanitize
 * @param sensitiveFields Array of sensitive field names
 * @returns Sanitized data as string
 */
function sanitizeData(data: any, sensitiveFields: string[] = []): string {
  if (!data) return '';
  
  // Create a copy of the data to avoid modifying the original
  const sanitized = { ...data };
  
  // Replace sensitive fields with asterisks
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '********';
    }
  });
  
  return JSON.stringify(sanitized);
} 