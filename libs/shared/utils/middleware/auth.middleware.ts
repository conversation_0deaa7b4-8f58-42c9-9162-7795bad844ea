import { Request, Response, NextFunction } from 'express';
import * as jwt from 'jsonwebtoken';

/**
 * Error interface for error handling
 */
export interface AppError extends Error {
  statusCode?: number;
  status?: string;
  isOperational?: boolean;
}

/**
 * Creates an operational error
 * @param message Error message
 * @param statusCode HTTP status code
 * @returns AppError
 */
export const createError = (message: string, statusCode: number = 400): AppError => {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
  error.isOperational = true;
  return error;
};

/**
 * Interface for decoded JWT token
 */
export interface DecodedToken {
  id: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * Extend Express Request interface to include user
 */
declare global {
  namespace Express {
    interface Request {
      user?: DecodedToken;
    }
  }
}

/**
 * Protects routes by requiring authentication
 * @param jwtSecret Secret key for JWT verification
 * @returns Middleware function
 */
export const createProtectMiddleware = (jwtSecret: string) => {
  return async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      // Check if token exists
      const authHeader = req.headers.authorization;
      let token: string | undefined;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.split(' ')[1];
      }
      
      if (!token) {
        throw createError('You are not logged in. Please log in to get access.', 401);
      }
      
      // Verify token
      try {
        const decoded = jwt.verify(token, jwtSecret) as DecodedToken;
        req.user = decoded;
        next();
      } catch (error: any) {
        console.error('JWT Verification failed:', error?.message);
        throw createError('Invalid token. Please log in again.', 401);
      }
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Restricts access to specific roles
 * @param roles Array of allowed roles
 * @returns Middleware function
 */
export const restrictTo = (...roles: string[]) => {
  return (req: Request, _res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw createError('You are not logged in. Please log in to get access.', 401);
      }
      
      if (!roles.includes(req.user.role)) {
        throw createError('You do not have permission to perform this action.', 403);
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
}; 