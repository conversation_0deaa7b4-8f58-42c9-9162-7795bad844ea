/**
 * Interface for database connection configuration
 */
export interface DatabaseConfig {
  debug?: boolean;
  // Common configuration properties can be added here
}

/**
 * Abstract base connector class for database connections
 * Provides a template pattern for creating database connectors
 */
export abstract class BaseConnector<T, C extends DatabaseConfig> {
  protected client: T | null = null;
  protected config: C;
  
  /**
   * Creates a new database connector
   * @param config Database connection configuration
   */
  constructor(config: C) {
    this.config = config;
  }
  
  /**
   * Connect to the database
   * @returns Promise resolving to the database client
   */
  abstract connect(): Promise<T>;
  
  /**
   * Disconnect from the database
   */
  abstract disconnect(): Promise<void>;
  
  /**
   * Get the current database client
   * @returns The database client
   */
  getClient(): T | null {
    return this.client;
  }
  
  /**
   * Check if connected to the database
   * (Implementation to be provided by subclasses)
   */
  abstract isConnected(): boolean;
} 