import { Connector, DatabaseConfig } from './interfaces/connector.interface';

/**
 * Abstract base connector class for database connections
 * Provides a template pattern for creating database connectors
 */
export abstract class AbstractConnector<T, C extends DatabaseConfig> implements Connector<T> {
  protected client: T | null = null;
  protected config: C;
  protected isConnectedFlag: boolean = false;
  
  /**
   * Creates a new database connector
   * @param config Database connection configuration
   */
  constructor(config: C) {
    this.config = config;
  }
  
  /**
   * Connect to the database - to be implemented by subclasses
   * @returns Promise resolving to the database client
   */
  abstract connect(): Promise<T>;
  
  /**
   * Disconnect from the database - to be implemented by subclasses
   */
  abstract disconnect(): Promise<void>;
  
  /**
   * Get the current database client
   * @returns The database client
   */
  getClient(): T | null {
    return this.client;
  }
  
  /**
   * Check if connected to the database
   * @returns True if connected, false otherwise
   */
  isConnected(): boolean {
    return this.isConnectedFlag;
  }
  
  /**
   * Log message if debug mode is enabled
   * @param message Message to log
   */
  protected logDebug(message: string): void {
    if (this.config.debug) {
      console.log(`[Database] ${message}`);
    }
  }
} 