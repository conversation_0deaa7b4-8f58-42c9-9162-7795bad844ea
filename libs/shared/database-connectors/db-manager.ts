import { Client } from '@elastic/elasticsearch';
import { Connection } from 'mongoose';
import { ElasticSearchConfig, ElasticSearchConnector, initElasticSearch } from './elastic.connector';
import { MongoDBConfig, MongoDBConnector, initMongoDB } from './mongo.connector';

/**
 * Database Manager class for handling multiple database connections
 */
export class DatabaseManager {
  private static instance: DatabaseManager | null = null;
  private mongoConnector: MongoDBConnector | null = null;
  private elasticConnector: ElasticSearchConnector | null = null;
  
  /**
   * Get the singleton instance of DatabaseManager
   * @returns The DatabaseManager instance
   */
  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }
  
  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {}
  
  /**
   * Initialize MongoDB connection
   * @param config MongoDB configuration
   * @returns Promise resolving to the MongoDB connection
   */
  async initMongoDB(config: MongoDBConfig): Promise<Connection> {
    const connection = await initMongoDB(config);
    this.mongoConnector = MongoDBConnector.getInstance(config);
    return connection;
  }
  
  /**
   * Initialize ElasticSearch connection
   * @param config ElasticSearch configuration
   * @returns Promise resolving to the ElasticSearch client
   */
  async initElasticSearch(config: ElasticSearchConfig): Promise<Client> {
    const client = await initElasticSearch(config);
    this.elasticConnector = ElasticSearchConnector.getInstance(config);
    return client;
  }
  
  /**
   * Get MongoDB connector
   * @returns The MongoDB connector instance
   */
  getMongoDBConnector(): MongoDBConnector | null {
    return this.mongoConnector;
  }
  
  /**
   * Get ElasticSearch connector
   * @returns The ElasticSearch connector instance
   */
  getElasticSearchConnector(): ElasticSearchConnector | null {
    return this.elasticConnector;
  }
  
  /**
   * Close all database connections
   * @returns Promise resolving when all connections are closed
   */
  async closeAll(): Promise<void> {
    const promises: Promise<void>[] = [];
    
    if (this.mongoConnector && this.mongoConnector.isConnected()) {
      promises.push(this.mongoConnector.disconnect());
    }
    
    if (this.elasticConnector && this.elasticConnector.isConnected()) {
      promises.push(this.elasticConnector.disconnect());
    }
    
    await Promise.all(promises);
  }
}

// Export singleton instance
export const dbManager = DatabaseManager.getInstance(); 