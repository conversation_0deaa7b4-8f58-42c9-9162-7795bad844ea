import { Client } from '@elastic/elasticsearch';
import { ElasticSearchConnector } from '../../elastic.connector';
import { ProductDocument, productIndexConfig } from '../../schemas/elastic/product.schema';
import { SearchQuery, buildElasticsearchQuery, transformElasticsearchResponse, SearchResults } from '../../schemas/elastic/search.schema';

/**
 * Repository for product-related Elasticsearch operations
 */
export class ProductRepository {
  private connector: ElasticSearchConnector;
  private client: Client;
  private indexName: string = 'products';
  
  /**
   * Creates a new product repository
   * @param connector ElasticSearch connector instance
   */
  constructor(connector: ElasticSearchConnector) {
    this.connector = connector;
    this.client = connector.getClient() as Client;
  }
  
  /**
   * Initialize the product index if it doesn't exist
   */
  async initIndex(): Promise<void> {
    try {
      const indexExists = await this.client.indices.exists({
        index: this.indexName,
      });
      
      if (!indexExists) {
        await this.client.indices.create({
          index: this.indexName,
          body: productIndexConfig as any,
        });
        
        console.log(`Created index: ${this.indexName}`);
      }
    } catch (error) {
      console.error('Error initializing product index:', error);
      throw error;
    }
  }
  
  /**
   * Index a product in Elasticsearch
   * @param product The product to index
   * @returns The indexed product with ID
   */
  async indexProduct(product: ProductDocument): Promise<ProductDocument> {
    try {
      const timestamp = new Date().toISOString();
      
      // Set created/updated timestamps if not provided
      if (!product.createdAt) {
        product.createdAt = timestamp;
      }
      
      product.updatedAt = timestamp;
      
      const response = await this.client.index({
        index: this.indexName,
        id: product.id,
        document: product,
        refresh: true, // Make the document immediately available for search
      });
      
      console.log(`Indexed product ${product.id} with result: ${response.result}`);
      return product;
    } catch (error) {
      console.error('Error indexing product:', error);
      throw error;
    }
  }
  
  /**
   * Get a product by ID
   * @param id The product ID to get
   * @returns The product document or null if not found
   */
  async getById(id: string): Promise<ProductDocument | null> {
    try {
      const response = await this.client.get({
        index: this.indexName,
        id: id,
      });
      
      if (response && response._source) {
        return response._source as ProductDocument;
      }
      
      return null;
    } catch (error) {
      if ((error as any).statusCode === 404) {
        return null;
      }
      console.error('Error getting product by ID:', error);
      throw error;
    }
  }
  
  /**
   * Update a product by ID
   * @param id The product ID to update
   * @param productData The partial product data to update
   * @returns The updated product
   */
  async updateProduct(id: string, productData: Partial<ProductDocument>): Promise<ProductDocument | null> {
    try {
      // First check if the product exists
      const existingProduct = await this.getById(id);
      if (!existingProduct) {
        return null;
      }
      
      // Update timestamp
      productData.updatedAt = new Date().toISOString();
      
      await this.client.update({
        index: this.indexName,
        id: id,
        doc: productData,
      });
      
      // Get the updated product
      return await this.getById(id);
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }
  
  /**
   * Delete a product by ID
   * @param id The product ID to delete
   * @returns True if deleted, false if not found
   */
  async deleteProduct(id: string): Promise<boolean> {
    try {
      const response = await this.client.delete({
        index: this.indexName,
        id: id,
        refresh: true,
      });
      
      return response.result === 'deleted';
    } catch (error) {
      if ((error as any).statusCode === 404) {
        return false;
      }
      console.error('Error deleting product:', error);
      throw error;
    }
  }
  
  /**
   * Search for products
   * @param searchQuery The search query parameters
   * @returns Search results
   */
  async searchProducts(searchQuery: SearchQuery): Promise<SearchResults<ProductDocument>> {
    try {
      const esQuery = buildElasticsearchQuery(searchQuery);
      
      const response = await this.client.search({
        index: this.indexName,
        body: esQuery,
      });
      
      const page = searchQuery.pagination?.page || 1;
      const size = searchQuery.pagination?.size || 10;
      
      return transformElasticsearchResponse<ProductDocument>(response, page, size);
    } catch (error) {
      console.error('Error searching products:', error);
      throw error;
    }
  }
  
  /**
   * Get products by farmer ID
   * @param farmerId The farmer ID
   * @param page Page number
   * @param size Page size
   * @returns Products for the farmer
   */
  async getProductsByFarmerId(farmerId: string, page: number = 1, size: number = 10): Promise<SearchResults<ProductDocument>> {
    try {
      const searchQuery: SearchQuery = {
        filters: {
          farmerId: farmerId,
        },
        pagination: {
          page,
          size,
        },
        sort: {
          field: 'createdAt',
          order: 'desc',
        },
      };

      return await this.searchProducts(searchQuery);
    } catch (error) {
      console.error('Error getting products by farmer ID:', error);
      throw error;
    }
  }
  
  /**
   * Get products by type
   * @param type The product type
   * @param page Page number
   * @param size Page size
   * @returns Products of the specified type
   */
  async getProductsByType(type: string, page: number = 1, size: number = 10): Promise<SearchResults<ProductDocument>> {
    try {
      const searchQuery: SearchQuery = {
        filters: {
          type: type,
        },
        pagination: {
          page,
          size,
        },
        sort: {
          field: 'createdAt',
          order: 'desc',
        },
      };
      
      return await this.searchProducts(searchQuery);
    } catch (error) {
      console.error('Error getting products by type:', error);
      throw error;
    }
  }
  
  /**
   * Bulk index products
   * @param products Array of products to index
   * @returns Number of products successfully indexed
   */
  async bulkIndexProducts(products: ProductDocument[]): Promise<number> {
    if (!products.length) {
      return 0;
    }
    
    try {
      const timestamp = new Date().toISOString();
      const operations = products.flatMap(product => {
        // Set created/updated timestamps if not provided
        if (!product.createdAt) {
          product.createdAt = timestamp;
        }
        
        product.updatedAt = timestamp;
        
        return [
          { index: { _index: this.indexName, _id: product.id } },
          product,
        ];
      });
      
      const response = await this.client.bulk({
        refresh: true,
        operations,
      });
      
      if (response.errors) {
        console.error('Bulk indexing had errors:', response.items);
        // Count successful operations
        const successful = response.items.filter(
          (item: any) => !item.index.error
        ).length;
        return successful;
      }
      
      return products.length;
    } catch (error) {
      console.error('Error bulk indexing products:', error);
      throw error;
    }
  }
} 