import { OrderDocument, OrderModel } from '../../schemas/mongo/order.schema';
import { MongoDBConnector } from '../../mongo.connector';

/**
 * Repository for order-related database operations
 */
export class OrderRepository {
  private connector: MongoDBConnector;
  
  /**
   * Creates a new order repository
   * @param connector MongoDB connector instance
   */
  constructor(connector: MongoDBConnector) {
    this.connector = connector;
  }
  
  /**
   * Find an order by ID
   * @param orderId The order ID to find
   * @returns The order document or null if not found
   */
  async findById(orderId: string): Promise<OrderDocument | null> {
    try {
      return await OrderModel.findOne({ orderId }).exec();
    } catch (error) {
      console.error('Error finding order by ID:', error);
      throw error;
    }
  }
  
  /**
   * Find orders by farmer ID
   * @param farmerId The farmer ID to find orders for
   * @returns Array of order documents
   */
  async findByFarmerId(farmerId: string): Promise<OrderDocument[]> {
    try {
      return await OrderModel.find({ farmerId }).exec();
    } catch (error) {
      console.error('Error finding orders by farmer ID:', error);
      throw error;
    }
  }
  
  /**
   * Find orders by buyer ID
   * @param buyerId The buyer ID to find orders for
   * @returns Array of order documents
   */
  async findByBuyerId(buyerId: string): Promise<OrderDocument[]> {
    try {
      return await OrderModel.find({ buyerId }).exec();
    } catch (error) {
      console.error('Error finding orders by buyer ID:', error);
      throw error;
    }
  }
  
  /**
   * Find orders by status
   * @param status The status to filter by
   * @returns Array of order documents
   */
  async findByStatus(status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'COMPLETED' | 'CANCELLED'): Promise<OrderDocument[]> {
    try {
      return await OrderModel.find({ status }).exec();
    } catch (error) {
      console.error('Error finding orders by status:', error);
      throw error;
    }
  }
  
  /**
   * Find orders by plot ID
   * @param plotId The plot ID to find orders for
   * @returns Array of order documents
   */
  async findByPlotId(plotId: string): Promise<OrderDocument[]> {
    try {
      return await OrderModel.find({ plotId }).exec();
    } catch (error) {
      console.error('Error finding orders by plot ID:', error);
      throw error;
    }
  }
  
  /**
   * Create a new order
   * @param orderData The order data to create
   * @returns The created order document
   */
  async create(orderData: Partial<OrderDocument>): Promise<OrderDocument> {
    try {
      const order = new OrderModel(orderData);
      return await order.save();
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }
  
  /**
   * Update an order by ID
   * @param orderId The order ID to update
   * @param updateData The data to update
   * @returns The updated order document or null if not found
   */
  async update(orderId: string, updateData: Partial<OrderDocument>): Promise<OrderDocument | null> {
    try {
      return await OrderModel.findOneAndUpdate(
        { orderId },
        { $set: updateData },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  }
  
  /**
   * Delete an order by ID
   * @param orderId The order ID to delete
   * @returns True if deleted, false if not found
   */
  async delete(orderId: string): Promise<boolean> {
    try {
      const result = await OrderModel.deleteOne({ orderId }).exec();
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting order:', error);
      throw error;
    }
  }
  
  /**
   * Find all orders with pagination
   * @param page Page number (1-based)
   * @param limit Number of items per page
   * @returns Object with orders array and pagination info
   */
  async findAll(page: number = 1, limit: number = 10): Promise<{ orders: OrderDocument[]; total: number; pages: number }> {
    try {
      const skip = (page - 1) * limit;
      const [orders, total] = await Promise.all([
        OrderModel.find().skip(skip).limit(limit).exec(),
        OrderModel.countDocuments(),
      ]);
      
      return {
        orders,
        total,
        pages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error('Error finding all orders:', error);
      throw error;
    }
  }
  
  /**
   * Update order status
   * @param orderId The order ID
   * @param status The new status
   * @param updateTimeline Whether to update the timeline based on the status
   * @returns The updated order document or null if not found
   */
  async updateStatus(
    orderId: string,
    status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'COMPLETED' | 'CANCELLED',
    updateTimeline: boolean = true
  ): Promise<OrderDocument | null> {
    try {
      const updateData: any = { status };
      
      // Update the timeline based on the status if requested
      if (updateTimeline) {
        switch (status) {
          case 'CONFIRMED':
            updateData['timeline.confirmationDate'] = new Date();
            break;
          case 'PROCESSING':
            updateData['timeline.processingDate'] = new Date();
            break;
          case 'COMPLETED':
            updateData['timeline.completionDate'] = new Date();
            break;
        }
      }
      
      return await OrderModel.findOneAndUpdate(
        { orderId },
        { $set: updateData },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }
  
  /**
   * Update payment status
   * @param orderId The order ID
   * @param paymentStatus The new payment status
   * @param transactionId Optional transaction ID
   * @returns The updated order document or null if not found
   */
  async updatePaymentStatus(
    orderId: string,
    paymentStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED',
    transactionId?: string
  ): Promise<OrderDocument | null> {
    try {
      const updateData: any = { 'payment.status': paymentStatus };
      
      if (transactionId) {
        updateData['payment.transactionId'] = transactionId;
      }
      
      return await OrderModel.findOneAndUpdate(
        { orderId },
        { $set: updateData },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error updating payment status:', error);
      throw error;
    }
  }
  
  /**
   * Add a message to an order
   * @param orderId The order ID
   * @param message The message to add
   * @returns The updated order document or null if not found
   */
  async addMessage(orderId: string, message: string): Promise<OrderDocument | null> {
    try {
      return await OrderModel.findOneAndUpdate(
        { orderId },
        { $push: { 'communication.messages': message } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error adding message to order:', error);
      throw error;
    }
  }
  
  /**
   * Add a notification to an order
   * @param orderId The order ID
   * @param notification The notification to add
   * @returns The updated order document or null if not found
   */
  async addNotification(orderId: string, notification: string): Promise<OrderDocument | null> {
    try {
      return await OrderModel.findOneAndUpdate(
        { orderId },
        { $push: { 'communication.notifications': notification } },
        { new: true }
      ).exec();
    } catch (error) {
      console.error('Error adding notification to order:', error);
      throw error;
    }
  }
} 