/**
 * Base configuration interface for all database connections
 */
export interface DatabaseConfig {
  debug?: boolean;
  // Add any common configuration properties here
}

/**
 * Base connector interface for all database types
 */
export interface Connector<T> {
  /**
   * Connect to the database
   * @returns Promise resolving to the database client
   */
  connect(): Promise<T>;
  
  /**
   * Disconnect from the database
   */
  disconnect(): Promise<void>;
  
  /**
   * Get the database client
   * @returns The database client
   */
  getClient(): T | null;
  
  /**
   * Check if connected to the database
   * @returns True if connected, false otherwise
   */
  isConnected(): boolean;
} 