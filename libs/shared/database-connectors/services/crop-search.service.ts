import { Client } from '@elastic/elasticsearch';
import { CropDocument, cropSearchConfigs } from '../schemas/elastic/crop.schema';
import { SearchQuery, SearchResults, buildElasticsearchQuery, transformElasticsearchResponse } from '../schemas/elastic/search.schema';

/**
 * Enhanced search filters for crop products
 */
export interface CropSearchFilters {
  // Location filters
  country?: string;
  state?: string;
  city?: string;
  pincode?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
    radius: number; // in kilometers
  };
  
  // Product filters
  isForSale?: boolean;
  priceRange?: {
    min: number;
    max: number;
    currency?: string;
  };
  availabilityStatus?: 'AVAILABLE' | 'LIMITED' | 'OUT_OF_STOCK' | 'PRE_ORDER';
  qualityGrade?: 'PREMIUM' | 'STANDARD' | 'ECONOMY';
  minQuantity?: number;
  maxQuantity?: number;
  
  // Crop characteristics
  cropCategory?: string;
  subCategory?: string;
  farmingMethod?: string;
  irrigationMethod?: string;
  harvestSeason?: string;
  growthStage?: string;
  healthStatus?: string;
  
  // Seller filters
  sellerId?: string;
  sellerRating?: number;
  sellerCertifications?: string[];
  
  // Date filters
  plantingDateFrom?: string;
  plantingDateTo?: string;
  harvestDateFrom?: string;
  harvestDateTo?: string;
  
  // Sustainability filters
  organicCertified?: boolean;
  fairTradeCertified?: boolean;
  sustainabilityScore?: number;
  
  // Delivery options
  deliveryMethods?: string[];
  maxDeliveryRadius?: number;
}

/**
 * Search options for different search types
 */
export interface CropSearchOptions {
  searchType?: 'product' | 'location' | 'seasonal' | 'general';
  includeOutOfStock?: boolean;
  sortBy?: 'relevance' | 'price' | 'distance' | 'harvest_date' | 'popularity' | 'quality';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  size?: number;
  includeAggregations?: boolean;
  userLocation?: {
    latitude: number;
    longitude: number;
  };
}

/**
 * Enhanced crop search service for better product discovery
 */
export class CropSearchService {
  private esClient: Client;
  private indexName: string;

  constructor(esClient: Client, indexName: string = 'befarma') {
    this.esClient = esClient;
    this.indexName = indexName;
  }

  /**
   * Search crops with enhanced filtering and relevance
   */
  async searchCrops(
    query: string,
    filters: CropSearchFilters = {},
    options: CropSearchOptions = {}
  ): Promise<SearchResults<CropDocument>> {
    const {
      searchType = 'general',
      includeOutOfStock = false,
      sortBy = 'relevance',
      sortOrder = 'desc',
      page = 1,
      size = 20,
      includeAggregations = true,
      userLocation
    } = options;

    // Build the base Elasticsearch query
    const esQuery = this.buildEnhancedQuery(query, filters, searchType, includeOutOfStock);
    
    // Add sorting
    const sort = this.buildSortQuery(sortBy, sortOrder, userLocation);
    
    // Add aggregations for faceted search
    const aggregations = includeAggregations ? this.buildAggregations() : undefined;

    try {
      const searchParams = {
        index: this.indexName,
        query: esQuery,
        sort,
        from: (page - 1) * size,
        size,
        ...(aggregations && { aggs: aggregations }),
        highlight: {
          fields: {
            name: {},
            'attributes.tags': {},
            'seller.name': {},
            'farm.name': {}
          }
        }
      };

      const response = await this.esClient.search(searchParams);
      return transformElasticsearchResponse<CropDocument>(response, page, size);
    } catch (error) {
      console.error('Error searching crops:', error);
      throw error;
    }
  }

  /**
   * Get crop suggestions for autocomplete
   */
  async getCropSuggestions(
    query: string,
    filters: Partial<CropSearchFilters> = {},
    limit: number = 10
  ): Promise<string[]> {
    try {
      const response = await this.esClient.search({
        index: this.indexName,
        query: {
          bool: {
            must: [
              {
                multi_match: {
                  query,
                  fields: ['name', 'type', 'variety', 'attributes.cropCategory'],
                  type: 'bool_prefix'
                }
              }
            ],
            filter: this.buildFilterQuery(filters)
          }
        },
        _source: ['name', 'type', 'variety'],
        size: limit
      });

      const suggestions = new Set<string>();
      response.hits.hits.forEach((hit: any) => {
        const source = hit._source;
        suggestions.add(source.name);
        suggestions.add(source.type);
        if (source.variety) suggestions.add(source.variety);
      });

      return Array.from(suggestions).slice(0, limit);
    } catch (error) {
      console.error('Error getting crop suggestions:', error);
      throw error;
    }
  }

  /**
   * Search crops by location with geo-distance
   */
  async searchCropsByLocation(
    coordinates: { latitude: number; longitude: number },
    radius: number = 50,
    filters: CropSearchFilters = {},
    options: CropSearchOptions = {}
  ): Promise<SearchResults<CropDocument>> {
    const geoQuery = {
      bool: {
        should: [
          {
            geo_distance: {
              distance: `${radius}km`,
              'seller.location.coordinates': {
                lat: coordinates.latitude,
                lon: coordinates.longitude
              }
            }
          },
          {
            geo_distance: {
              distance: `${radius}km`,
              'farm.location.coordinates': {
                lat: coordinates.latitude,
                lon: coordinates.longitude
              }
            }
          }
        ],
        minimum_should_match: 1,
        filter: this.buildFilterQuery(filters)
      }
    };

    try {
      const response = await this.esClient.search({
        index: this.indexName,
        query: geoQuery as any,
        sort: [
          {
            _geo_distance: {
              'seller.location.coordinates': {
                lat: coordinates.latitude,
                lon: coordinates.longitude
              },
              order: 'asc',
              unit: 'km'
            }
          }
        ],
        from: ((options.page || 1) - 1) * (options.size || 20),
        size: options.size || 20
      });

      return transformElasticsearchResponse<CropDocument>(response, options.page || 1, options.size || 20);
    } catch (error) {
      console.error('Error searching crops by location:', error);
      throw error;
    }
  }

  /**
   * Get popular crops based on search metadata
   */
  async getPopularCrops(
    filters: Partial<CropSearchFilters> = {},
    limit: number = 10
  ): Promise<CropDocument[]> {
    try {
      const response = await this.esClient.search({
        index: this.indexName,
        query: {
          bool: {
            filter: this.buildFilterQuery(filters)
          }
        },
        sort: [
          { 'searchMetadata.popularityScore': { order: 'desc' } },
          { 'searchMetadata.searchCount': { order: 'desc' } }
        ],
        size: limit
      });

      return response.hits.hits.map((hit: any) => hit._source);
    } catch (error) {
      console.error('Error getting popular crops:', error);
      throw error;
    }
  }

  /**
   * Build enhanced Elasticsearch query based on search type
   */
  private buildEnhancedQuery(
    query: string,
    filters: CropSearchFilters,
    searchType: string,
    includeOutOfStock: boolean
  ): any {
    const config = cropSearchConfigs[searchType as keyof typeof cropSearchConfigs] || cropSearchConfigs.productSearch;
    
    const boolQuery: any = {
      bool: {
        must: [],
        filter: [],
        should: [],
        must_not: []
      }
    };

    // Add text search if query provided
    if (query && query.trim()) {
      boolQuery.bool.must.push({
        multi_match: {
          query: query.trim(),
          fields: config.fields,
          type: 'best_fields',
          fuzziness: 'AUTO',
          boost: 1.2
        }
      });
    }

    // Add filters
    const filterQueries = this.buildFilterQuery(filters);
    boolQuery.bool.filter.push(...filterQueries);

    // Exclude out of stock items unless explicitly included
    if (!includeOutOfStock) {
      boolQuery.bool.must_not.push({
        term: { 'product.availability.status': 'OUT_OF_STOCK' }
      });
    }

    return boolQuery;
  }

  /**
   * Build filter queries from search filters
   */
  private buildFilterQuery(filters: Partial<CropSearchFilters>): any[] {
    const filterQueries: any[] = [];

    // Location filters
    if (filters.country) {
      filterQueries.push({ term: { 'seller.location.country': filters.country } });
    }
    if (filters.state) {
      filterQueries.push({ term: { 'seller.location.state': filters.state } });
    }
    if (filters.city) {
      filterQueries.push({ term: { 'seller.location.city': filters.city } });
    }
    if (filters.pincode) {
      filterQueries.push({ term: { 'seller.location.pincode': filters.pincode } });
    }

    // Product filters
    if (filters.isForSale !== undefined) {
      filterQueries.push({ term: { 'product.isForSale': filters.isForSale } });
    }
    if (filters.availabilityStatus) {
      filterQueries.push({ term: { 'product.availability.status': filters.availabilityStatus } });
    }
    if (filters.qualityGrade) {
      filterQueries.push({ term: { 'product.qualityGrade': filters.qualityGrade } });
    }

    // Price range filter
    if (filters.priceRange) {
      const priceFilter: any = { range: { 'product.price.amount': {} } };
      if (filters.priceRange.min !== undefined) {
        priceFilter.range['product.price.amount'].gte = filters.priceRange.min;
      }
      if (filters.priceRange.max !== undefined) {
        priceFilter.range['product.price.amount'].lte = filters.priceRange.max;
      }
      filterQueries.push(priceFilter);
    }

    // Quantity filters
    if (filters.minQuantity !== undefined) {
      filterQueries.push({
        range: { 'product.availability.quantity': { gte: filters.minQuantity } }
      });
    }
    if (filters.maxQuantity !== undefined) {
      filterQueries.push({
        range: { 'product.availability.quantity': { lte: filters.maxQuantity } }
      });
    }

    // Crop characteristic filters
    if (filters.cropCategory) {
      filterQueries.push({ term: { 'attributes.cropCategory': filters.cropCategory } });
    }
    if (filters.subCategory) {
      filterQueries.push({ term: { 'attributes.subCategory': filters.subCategory } });
    }
    if (filters.farmingMethod) {
      filterQueries.push({ term: { 'attributes.farmingMethod': filters.farmingMethod } });
    }
    if (filters.irrigationMethod) {
      filterQueries.push({ term: { 'attributes.irrigationMethod': filters.irrigationMethod } });
    }
    if (filters.harvestSeason) {
      filterQueries.push({ term: { 'attributes.harvestSeason': filters.harvestSeason } });
    }
    if (filters.growthStage) {
      filterQueries.push({ term: { growthStage: filters.growthStage } });
    }
    if (filters.healthStatus) {
      filterQueries.push({ term: { 'healthStatus.status': filters.healthStatus } });
    }

    // Seller filters
    if (filters.sellerId) {
      filterQueries.push({ term: { sellerId: filters.sellerId } });
    }
    if (filters.sellerRating !== undefined) {
      filterQueries.push({
        range: { 'seller.rating': { gte: filters.sellerRating } }
      });
    }
    if (filters.sellerCertifications && filters.sellerCertifications.length > 0) {
      filterQueries.push({
        terms: { 'seller.certifications': filters.sellerCertifications }
      });
    }

    // Date range filters
    if (filters.plantingDateFrom || filters.plantingDateTo) {
      const dateFilter: any = { range: { plantingDate: {} } };
      if (filters.plantingDateFrom) {
        dateFilter.range.plantingDate.gte = filters.plantingDateFrom;
      }
      if (filters.plantingDateTo) {
        dateFilter.range.plantingDate.lte = filters.plantingDateTo;
      }
      filterQueries.push(dateFilter);
    }

    if (filters.harvestDateFrom || filters.harvestDateTo) {
      const dateFilter: any = { range: { expectedHarvestDate: {} } };
      if (filters.harvestDateFrom) {
        dateFilter.range.expectedHarvestDate.gte = filters.harvestDateFrom;
      }
      if (filters.harvestDateTo) {
        dateFilter.range.expectedHarvestDate.lte = filters.harvestDateTo;
      }
      filterQueries.push(dateFilter);
    }

    // Sustainability filters
    if (filters.organicCertified !== undefined) {
      filterQueries.push({ term: { 'compliance.organicCertified': filters.organicCertified } });
    }
    if (filters.fairTradeCertified !== undefined) {
      filterQueries.push({ term: { 'compliance.fairTradeCertified': filters.fairTradeCertified } });
    }

    // Delivery filters
    if (filters.deliveryMethods && filters.deliveryMethods.length > 0) {
      filterQueries.push({
        terms: { 'product.delivery.methods': filters.deliveryMethods }
      });
    }
    if (filters.maxDeliveryRadius !== undefined) {
      filterQueries.push({
        range: { 'product.delivery.radius': { lte: filters.maxDeliveryRadius } }
      });
    }

    return filterQueries;
  }

  /**
   * Build sort query based on sort criteria
   */
  private buildSortQuery(
    sortBy: string,
    sortOrder: string,
    userLocation?: { latitude: number; longitude: number }
  ): any[] {
    const sort: any[] = [];

    switch (sortBy) {
      case 'price':
        sort.push({ 'product.price.amount': { order: sortOrder } });
        break;
      case 'distance':
        if (userLocation) {
          sort.push({
            _geo_distance: {
              'seller.location.coordinates': {
                lat: userLocation.latitude,
                lon: userLocation.longitude
              },
              order: sortOrder,
              unit: 'km'
            }
          });
        }
        break;
      case 'harvest_date':
        sort.push({ expectedHarvestDate: { order: sortOrder } });
        break;
      case 'popularity':
        sort.push({ 'searchMetadata.popularityScore': { order: sortOrder } });
        break;
      case 'quality':
        sort.push({ 'searchMetadata.qualityScore': { order: sortOrder } });
        break;
      case 'relevance':
      default:
        sort.push({ _score: { order: 'desc' } });
        break;
    }

    // Always add a secondary sort for consistent results
    if (sortBy !== 'relevance') {
      sort.push({ _score: { order: 'desc' } });
    }
    sort.push({ createdAt: { order: 'desc' } });

    return sort;
  }

  /**
   * Build aggregations for faceted search
   */
  private buildAggregations(): any {
    return {
      crop_categories: {
        terms: { field: 'attributes.cropCategory', size: 20 }
      },
      farming_methods: {
        terms: { field: 'attributes.farmingMethod', size: 10 }
      },
      harvest_seasons: {
        terms: { field: 'attributes.harvestSeason', size: 10 }
      },
      quality_grades: {
        terms: { field: 'product.qualityGrade', size: 5 }
      },
      availability_status: {
        terms: { field: 'product.availability.status', size: 5 }
      },
      states: {
        terms: { field: 'seller.location.state', size: 20 }
      },
      cities: {
        terms: { field: 'seller.location.city', size: 50 }
      },
      price_ranges: {
        range: {
          field: 'product.price.amount',
          ranges: [
            { to: 100 },
            { from: 100, to: 500 },
            { from: 500, to: 1000 },
            { from: 1000, to: 5000 },
            { from: 5000 }
          ]
        }
      },
      growth_stages: {
        terms: { field: 'growthStage', size: 10 }
      },
      certifications: {
        terms: { field: 'seller.certifications', size: 15 }
      }
    };
  }
}
