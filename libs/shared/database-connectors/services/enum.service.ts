import { EnumModel, EnumCategoryType, EnumDocument } from '../schemas/mongo/enums.schema';

/**
 * Enum Service - Service for handling enum operations
 */
export class EnumService {
  /**
   * Get all enums of a specific category
   * @param category The enum category
   * @returns Array of enum documents
   */
  static async getEnumsByCategory(category: EnumCategoryType): Promise<EnumDocument[]> {
    return EnumModel.find({ category, isActive: true }).lean();
  }

  /**
   * Get a specific enum by category and subcategory
   * @param category The enum category
   * @param subcategory The enum subcategory
   * @returns The enum document
   */
  static async getEnum(category: EnumCategoryType, subcategory: string): Promise<EnumDocument | null> {
    return EnumModel.findOne({ category, subcategory, isActive: true }).lean();
  }

  /**
   * Get values of a specific enum
   * @param category The enum category
   * @param subcategory The enum subcategory
   * @returns Array of enum values
   */
  static async getEnumValues(category: EnumCategoryType, subcategory: string): Promise<string[]> {
    const enumDoc = await EnumModel.findOne({ category, subcategory, isActive: true }).lean();
    return enumDoc ? enumDoc.values : [];
  }

  /**
   * Check if a value exists in a specific enum
   * @param category The enum category
   * @param subcategory The enum subcategory
   * @param value The value to check
   * @returns Boolean indicating if the value exists
   */
  static async isValidEnumValue(category: EnumCategoryType, subcategory: string, value: string): Promise<boolean> {
    const values = await this.getEnumValues(category, subcategory);
    return values.includes(value);
  }

  /**
   * Update the values of a specific enum
   * @param category The enum category
   * @param subcategory The enum subcategory
   * @param values The new values
   * @returns The updated enum document
   */
  static async updateEnumValues(category: EnumCategoryType, subcategory: string, values: string[]): Promise<EnumDocument | null> {
    return EnumModel.findOneAndUpdate(
      { category, subcategory },
      { values },
      { new: true }
    );
  }

  /**
   * Add a value to a specific enum
   * @param category The enum category
   * @param subcategory The enum subcategory
   * @param value The value to add
   * @returns The updated enum document
   */
  static async addEnumValue(category: EnumCategoryType, subcategory: string, value: string): Promise<EnumDocument | null> {
    const enumDoc = await EnumModel.findOne({ category, subcategory });
    if (!enumDoc) return null;

    if (!enumDoc.values.includes(value)) {
      enumDoc.values.push(value);
      return enumDoc.save();
    }

    return enumDoc;
  }

  /**
   * Remove a value from a specific enum
   * @param category The enum category
   * @param subcategory The enum subcategory
   * @param value The value to remove
   * @returns The updated enum document
   */
  static async removeEnumValue(category: EnumCategoryType, subcategory: string, value: string): Promise<EnumDocument | null> {
    const enumDoc = await EnumModel.findOne({ category, subcategory });
    if (!enumDoc) return null;

    enumDoc.values = enumDoc.values.filter(v => v !== value);
    return enumDoc.save();
  }

  /**
   * Create a new enum
   * @param name The enum name
   * @param category The enum category
   * @param subcategory The enum subcategory
   * @param values The enum values
   * @returns The created enum document
   */
  static async createEnum(name: string, category: EnumCategoryType, subcategory: string, values: string[]): Promise<EnumDocument> {
    return EnumModel.create({
      name,
      category,
      subcategory,
      values,
      isActive: true
    });
  }

  /**
   * Get all enums
   * @returns Array of all enum documents
   */
  static async getAllEnums(): Promise<EnumDocument[]> {
    return EnumModel.find({ isActive: true }).lean();
  }
}