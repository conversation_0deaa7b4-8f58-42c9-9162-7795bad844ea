import mongoose, { Connection, ConnectOptions } from 'mongoose';
import { DatabaseConfig } from './interfaces/connector.interface';
import { AbstractConnector } from './abstract-connector';

/**
 * Configuration interface for MongoDB connection
 */
export interface MongoDBConfig extends DatabaseConfig {
  uri: string;
  options?: ConnectOptions;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
}

/**
 * MongoDB connection manager
 */
export class MongoDBConnector extends AbstractConnector<mongoose.Connection, MongoDBConfig> {
  private static instance: MongoDBConnector | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 2000; // ms, will be doubled on each failure (exponential backoff)
  private reconnecting = false;

  /**
   * Get singleton instance of MongoDBConnector
   * @param config MongoDB configuration
   * @returns MongoDBConnector instance
   */
  public static getInstance(config: MongoDBConfig): MongoDBConnector {
    if (!MongoDBConnector.instance) {
      MongoDBConnector.instance = new MongoDBConnector(config);
    }
    return MongoDBConnector.instance;
  }

  /**
   * Creates a new MongoDB connector
   * @param config MongoDB configuration
   */
  private constructor(config: MongoDBConfig) {
    super(config);
    // Allow config overrides for reconnection
    if (typeof config.maxReconnectAttempts === 'number') {
      this.maxReconnectAttempts = config.maxReconnectAttempts;
    }
    if (typeof config.reconnectDelay === 'number') {
      this.reconnectDelay = config.reconnectDelay;
    }
  }

  /**
   * Connect to the MongoDB database
   * @returns Promise resolving to the Mongoose connection
   */
  async connect(): Promise<mongoose.Connection> {
    try {
      if (!this.client) {
        mongoose.set('strictQuery', false);
        await mongoose.connect(this.config.uri, {
          dbName: this.config.options?.dbName,
          ...this.config.options
        });
        this.client = mongoose.connection;
        this.attachEventListeners();
      }
      return this.client;
    } catch (error) {
      console.error('Error connecting to MongoDB:', error);
      this.isConnectedFlag = false;
      throw error;
    }
  }

  /**
   * Attach event listeners for connection state and reconnection
   */
  private attachEventListeners() {
    if (!this.client) return;
    // Avoid attaching multiple listeners
    this.client.removeAllListeners('disconnected');
    this.client.removeAllListeners('reconnected');
    this.client.removeAllListeners('error');
    this.client.removeAllListeners('open');

    this.client.on('disconnected', () => {
      this.isConnectedFlag = false;
      console.error('[MongoDBConnector] MongoDB disconnected! Will attempt to reconnect...');
      this.tryReconnect();
    });
    this.client.on('reconnected', () => {
      this.isConnectedFlag = true;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 2000;
      console.log('[MongoDBConnector] MongoDB reconnected!');
    });
    this.client.on('error', (err) => {
      console.error('[MongoDBConnector] MongoDB connection error:', err);
      this.isConnectedFlag = false;
    });
    this.client.once('open', () => {
      this.isConnectedFlag = true;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 2000;
      this.logDebug(`Connected to MongoDB database: ${this.config.options?.dbName || 'default'}`);
    });
  }

  /**
   * Attempt to reconnect with exponential backoff
   */
  private async tryReconnect() {
    if (this.reconnecting) return;
    this.reconnecting = true;
    while (!this.isConnectedFlag && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`[MongoDBConnector] Attempting to reconnect to MongoDB... (Attempt ${this.reconnectAttempts})`);
      try {
        await mongoose.connect(this.config.uri, {
          dbName: this.config.options?.dbName,
          ...this.config.options
        });
        this.client = mongoose.connection;
        this.attachEventListeners();
        if (this.client.readyState === 1) {
          this.isConnectedFlag = true;
          this.reconnectAttempts = 0;
          this.reconnectDelay = 2000;
          console.log('[MongoDBConnector] Successfully reconnected to MongoDB!');
          break;
        }
      } catch (err) {
        console.error(`[MongoDBConnector] Reconnection attempt failed:`, err);
        await new Promise(res => setTimeout(res, this.reconnectDelay));
        this.reconnectDelay = Math.min(this.reconnectDelay * 2, 60000); // Cap at 60s
      }
    }
    if (!this.isConnectedFlag) {
      console.error('[MongoDBConnector] Failed to reconnect to MongoDB after maximum attempts. Manual intervention required.');
    }
    this.reconnecting = false;
  }

  /**
   * Check if the connection is established
   * @returns True if connected, false otherwise
   */
  override isConnected(): boolean {
    return super.isConnected() && this.client !== null && this.client.readyState === 1;
  }

  /**
   * Disconnect from the MongoDB database
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      await mongoose.disconnect();
      this.client = null;
      this.isConnectedFlag = false;
      this.logDebug('Disconnected from MongoDB');
    }
  }

  /**
   * Get the current connection
   * @returns The Mongoose connection
   */
  getConnection(): mongoose.Connection | null {
    return this.client;
  }
}

/**
 * Initialize MongoDB connector with configuration
 * @param config MongoDB connection configuration
 * @returns Promise resolving when connected
 */
export async function initMongoDB(config: MongoDBConfig): Promise<Connection> {
  const connector = MongoDBConnector.getInstance(config);
  return connector.connect();
}

/**
 * Get MongoDB connection
 * This function is used for backward compatibility
 * @param credentials Optional credentials (not used, for compatibility)
 * @returns The MongoDB connection
 */
export function getMongoDb(credentials?: any): Connection | null {
  const connector = MongoDBConnector.getInstance({ uri: '' }); // Just to get the instance, config not used
  if (!connector.isConnected()) {
    console.warn('MongoDB not initialized. Call initMongoDB first.');
    return null;
  }
  return connector.getConnection();
} 