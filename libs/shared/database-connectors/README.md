# Database Connectors

Reusable, templated database connectors for MongoDB and ElasticSearch in TypeScript.

## Features

- Abstract base connector with template pattern
- Singleton pattern for connection management
- Type-safe configurations
- Connection pooling and management
- Standardized error handling
- Backward compatibility with existing code

## Usage Examples

### Initializing Database Connections

```typescript
import { dbManager, MongoDBConfig, ElasticSearchConfig } from '@libs/shared/database-connectors';

// Define MongoDB configuration
const mongoConfig: MongoDBConfig = {
  uri: 'mongodb://localhost:27017/mydatabase',
  options: {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  },
  debug: process.env.NODE_ENV === 'development',
};

// Define ElasticSearch configuration
const esConfig: ElasticSearchConfig = {
  node: 'http://localhost:9200',
  auth: {
    username: 'elastic',
    password: 'changeme',
  },
  debug: process.env.NODE_ENV === 'development',
};

// Initialize connections
async function initDatabases() {
  try {
    // Connect to MongoDB
    await dbManager.initMongoDB(mongoConfig);
  
    // Connect to ElasticSearch
    await dbManager.initElasticSearch(esConfig);
  
    console.log('All database connections established successfully');
  } catch (error) {
    console.error('Failed to initialize databases:', error);
  }
}

// Clean up connections when the application shuts down
process.on('SIGINT', async () => {
  await dbManager.closeAll();
  process.exit(0);
});
```

### Using MongoDB

```typescript
import { getMongoDb } from '@libs/shared/database-connectors';

// Using the MongoDB connection (backward compatibility)
async function findUsers() {
  const db = getMongoDb();
  if (!db) {
    throw new Error('MongoDB not initialized');
  }
  
  const collection = db.collection('users');
  return collection.find({ active: true }).toArray();
}

// Using the MongoDB connector with advanced features
import { dbManager } from '@libs/shared/database-connectors';

async function createUser(userData: any) {
  const mongoConnector = dbManager.getMongoDBConnector();
  if (!mongoConnector) {
    throw new Error('MongoDB connector not initialized');
  }
  
  const collection = mongoConnector.getCollection('users');
  return collection?.insertOne(userData);
}
```

### Using ElasticSearch

```typescript
import { getElasticSearchDb } from '@libs/shared/database-connectors';

// Using the ElasticSearch client (backward compatibility)
async function searchUsers(query: string) {
  const client = getElasticSearchDb();
  if (!client) {
    throw new Error('ElasticSearch not initialized');
  }
  
  return client.search({
    index: 'users',
    body: {
      query: {
        match: {
          name: query
        }
      }
    }
  });
}

// Using the ElasticSearch connector with advanced features
import { dbManager } from '@libs/shared/database-connectors';

async function indexDocument(document: any) {
  const esConnector = dbManager.getElasticSearchConnector();
  if (!esConnector) {
    throw new Error('ElasticSearch connector not initialized');
  }
  
  return esConnector.index({
    index: 'products',
    body: document
  });
}
```

## Creating Your Own Database Connector

You can easily extend the base connector to implement other database types:

```typescript
import { AbstractConnector, DatabaseConfig } from '@libs/shared/database-connectors';

// Define your database configuration
interface RedisConfig extends DatabaseConfig {
  host: string;
  port: number;
  password?: string;
}

// Create your Redis connector
class RedisConnector extends AbstractConnector<Redis, RedisConfig> {
  private static instance: RedisConnector | null = null;
  
  public static getInstance(config: RedisConfig): RedisConnector {
    if (!RedisConnector.instance) {
      RedisConnector.instance = new RedisConnector(config);
    }
    return RedisConnector.instance;
  }
  
  private constructor(config: RedisConfig) {
    super(config);
  }
  
  async connect(): Promise<Redis> {
    // Implementation specific to Redis
    // ...
    return this.client!;
  }
  
  async disconnect(): Promise<void> {
    // Implementation specific to Redis
    // ...
  }
  
  // Add Redis-specific methods
  async get(key: string): Promise<any> {
    // Implementation...
  }
  
  async set(key: string, value: any): Promise<void> {
    // Implementation...
  }
}
```
