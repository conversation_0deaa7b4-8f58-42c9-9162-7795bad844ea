/**
 * Crop growth stage enum
 */
export enum CropGrowthStage {
  PLANTING = 'PLANTING',
  GROWING = 'GROWING',
  MATURING = 'MATURING',
  READY = 'READY',
}

/**
 * Crop health status enum
 */
export enum CropHealthStatus {
  HEALTHY = 'HEALTHY',
  WARNING = 'WARNING',
  CRITICAL = 'CRITICAL',
}

/**
 * Crop document interface for Elasticsearch - Enhanced for product search
 */
export interface CropDocument {
  id: string;
  cropId: string;
  numberOfPlots: number;
  farmId: string;
  sellerId: string;

  // Basic crop information
  name: string;
  type: string;
  variety: string;
  plantingDate: string; // ISO date string
  expectedHarvestDate: string; // ISO date string
  actualHarvestDate?: string; // ISO date string
  growthStage: CropGrowthStage;
  healthStatus: {
    status: CropHealthStatus;
    issues: string[];
    lastCheck: string; // ISO date string
  };
  yield: {
    expected: number;
    actual: number;
    unit: string;
  };
  resources: {
    water: number;
    fertilizer: number;
    pesticides: number;
  };

  // Denormalized farmer information for better search
  seller: {
    sellerId: string;
    name: string;
    contact: string;
    email: string;
    location: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2: string;
    };
    certifications: string[];
    rating?: number;
    totalFarms?: number;
    experienceYears?: number;
  };

  // Denormalized farm information
  farm: {
    farmId: string;
    name: string;
    location: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2: string;
      coordinates?: {
        latitude: number;
        longitude: number;
      };
    };
    totalArea: number;
    soilType: string;
    waterSource: string;
    infrastructure: string[];
    certifications: string[];
  };



  // Product-specific information for marketplace
  product: {
    isForSale: boolean;
    price?: {
      amount: number;
      currency: string;
      unit: string; // per kg, per quintal, per acre, etc.
      negotiable: boolean;
    };
    availability: {
      status: 'AVAILABLE' | 'LIMITED' | 'OUT_OF_STOCK' | 'PRE_ORDER';
      quantity: number;
      unit: string;
      minOrderQuantity?: number;
      maxOrderQuantity?: number;
    };
    delivery: {
      methods: string[]; // 'PICKUP', 'DELIVERY', 'SHIPPING'
      charges?: number;
      estimatedTime?: string;
      radius?: number; // delivery radius in km
    };
    qualityGrade: 'PREMIUM' | 'STANDARD' | 'ECONOMY';
    packaging: {
      type: string;
      sizes: string[];
      customPackaging: boolean;
    };
  };
  // Enhanced attributes for better search and categorization
  attributes: {
    images?: string[];
    tags?: string[];

    // Crop-specific attributes
    cropCategory: string; // Vegetables, Fruits, Grains, Pulses, etc.
    subCategory?: string; // Leafy Vegetables, Root Vegetables, etc.
    farmingMethod: string; // Organic, Conventional, Biodynamic, etc.
    irrigationMethod: string; // Rain-fed, Canal, Drip, Sprinkler, etc.
    harvestSeason: string; // Kharif, Rabi, Zaid, Year-round
    pestDiseaseStatus: string; // None, Minor Infestation, Major Infestation, etc.
    storageMethod: string; // Open Storage, Cold Storage, Controlled Atmosphere, etc.
    nutrientManagement: string; // Organic Fertilizers, Chemical Fertilizers, Integrated, etc.
    waterSource: string; // Rainwater Harvesting, Groundwater, Canal, River, etc.
    pesticideUsage: string; // None, Organic Pesticides, Chemical Pesticides, IPM, etc.
    seedType: string; // Heirloom, Hybrid, GMO, Open Pollinated
    harvestingMethod: string; // Manual, Mechanical, Semi-mechanical

    // Enhanced soil and environmental conditions
    soilConditions?: {
      type: string;
      ph: number;
      nutrients: string[];
      organicMatter?: number;
      drainage?: string;
      salinity?: string;
    };
    waterAvailability?: string;
    climateZone?: string;
    altitude?: number;

    // Nutritional and health information
    nutritionalInfo?: {
      calories?: number;
      protein?: number;
      carbs?: number;
      fiber?: number;
      vitamins?: string[];
      minerals?: string[];
      antioxidants?: string[];
    };

    // Cultivation details
    cultivation?: {
      irrigationNeeds: string;
      fertilizerRequirements: string;
      pestControl: string;
      climateConditions: string;
      sowingMethod: string;
      spacingRequirements: string;
      companionCrops?: string[];
    };

    // Post-harvest and market information
    postHarvest?: {
      storageRequirements: string;
      shelfLife: string;
      processingMethods: string[];
      valueAddedProducts?: string[];
      marketDemand?: string;
      exportPotential?: boolean;
    };

    // Sustainability and environmental impact
    sustainability?: {
      waterUsage: string;
      carbonFootprint: string;
      pesticide: string;
      biodiversityImpact?: string;
      soilHealth?: string;
      renewableEnergy?: boolean;
    };

    // Market and business attributes
    market?: {
      demandLevel: 'HIGH' | 'MEDIUM' | 'LOW';
      priceVolatility: 'HIGH' | 'MEDIUM' | 'LOW';
      seasonalDemand: string[];
      targetMarkets: string[]; // Local, Regional, National, Export
      competitionLevel: 'HIGH' | 'MEDIUM' | 'LOW';
    };

    // Risk factors
    risks?: {
      weatherRisks: string[];
      pestRisks: string[];
      marketRisks: string[];
      mitigationStrategies?: string[];
    };
  };
  maintenance: {
    schedule: {
      irrigation: string[]; // Array of ISO date strings
      fertilization: string[]; // Array of ISO date strings
      pestControl: string[]; // Array of ISO date strings
      inspection: string[]; // Array of ISO date strings
    };
    history: {
      activities: [{
        type: string;
        date: string; // ISO date string
        description: string;
        performedBy: string;
      }];
    };
  };
  weather: {
    forecasts: [{
      date: string; // ISO date string
      temperature: {
        min: number;
        max: number;
        unit: string;
      };
      precipitation: {
        amount: number;
        unit: string;
        type: string; // Rain, Snow, etc.
      };
      humidity: number;
      windSpeed: {
        value: number;
        unit: string;
      };
    }];
    alerts: [{
      type: string;
      severity: string;
      description: string;
      startDate: string; // ISO date string
      endDate: string; // ISO date string
    }];
  };

  // Certifications and compliance
  certifications?: string[];
  compliance?: {
    organicCertified: boolean;
    fairTradeCertified: boolean;
    globalGAPCertified: boolean;
    otherCertifications: string[];
    lastAuditDate?: string;
    nextAuditDate?: string;
  };

  // Search and discovery metadata
  searchMetadata?: {
    searchKeywords: string[]; // Auto-generated keywords for better search
    popularityScore: number; // Based on views, searches, purchases
    seasonalRelevance: number; // Current seasonal relevance score
    qualityScore: number; // Overall quality score based on various factors
    lastSearched?: string;
    searchCount: number;
  };

  // Timestamps
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  lastSyncedAt?: string; // Last sync with MongoDB
}

/**
 * Crop mapping for Elasticsearch
 */
export const cropMapping = {
  mappings: {
    properties: {
      id: { type: 'keyword' },
      cropId: { type: 'keyword' },
      numberOfPlots: { type: 'integer' },
      farmId: { type: 'keyword' },
      sellerId: { type: 'keyword' },
      name: {
        type: 'text',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      type: { type: 'keyword' },
      variety: { type: 'keyword' },
      plantingDate: { type: 'date' },
      expectedHarvestDate: { type: 'date' },
      actualHarvestDate: { type: 'date' },
      growthStage: { type: 'keyword' },
      healthStatus: {
        properties: {
          status: { type: 'keyword' },
          issues: { type: 'keyword' },
          lastCheck: { type: 'date' }
        }
      },
      yield: {
        properties: {
          expected: { type: 'double' },
          actual: { type: 'double' },
          unit: { type: 'keyword' }
        }
      },
      resources: {
        properties: {
          water: { type: 'double' },
          fertilizer: { type: 'double' },
          pesticides: { type: 'double' }
        }
      },

      // Denormalized farmer information
      seller: {
        properties: {
          sellerId: { type: 'keyword' },
          name: {
            type: 'text',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          contact: { type: 'keyword' },
          email: { type: 'keyword' },
          location: {
            properties: {
              country: { type: 'keyword' },
              state: { type: 'keyword' },
              city: { type: 'keyword' },
              pincode: { type: 'keyword' },
              addressLine1: { type: 'text' },
              addressLine2: { type: 'text' }
            }
          },
          certifications: { type: 'keyword' },
          rating: { type: 'float' },
          totalFarms: { type: 'integer' },
          experienceYears: { type: 'integer' }
        }
      },

      // Denormalized farm information
      farm: {
        properties: {
          farmId: { type: 'keyword' },
          name: {
            type: 'text',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          location: {
            properties: {
              country: { type: 'keyword' },
              state: { type: 'keyword' },
              city: { type: 'keyword' },
              pincode: { type: 'keyword' },
              addressLine1: { type: 'text' },
              addressLine2: { type: 'text' },
              coordinates: {
                properties: {
                  latitude: { type: 'float' },
                  longitude: { type: 'float' }
                }
              }
            }
          },
          totalArea: { type: 'float' },
          soilType: { type: 'keyword' },
          waterSource: { type: 'keyword' },
          infrastructure: { type: 'keyword' },
          certifications: { type: 'keyword' }
        }
      },



      // Product information for marketplace
      product: {
        properties: {
          isForSale: { type: 'boolean' },
          price: {
            properties: {
              amount: { type: 'double' },
              currency: { type: 'keyword' },
              unit: { type: 'keyword' },
              negotiable: { type: 'boolean' }
            }
          },
          availability: {
            properties: {
              status: { type: 'keyword' },
              quantity: { type: 'double' },
              unit: { type: 'keyword' },
              minOrderQuantity: { type: 'double' },
              maxOrderQuantity: { type: 'double' }
            }
          },
          delivery: {
            properties: {
              methods: { type: 'keyword' },
              charges: { type: 'double' },
              estimatedTime: { type: 'keyword' },
              radius: { type: 'integer' }
            }
          },
          qualityGrade: { type: 'keyword' },
          packaging: {
            properties: {
              type: { type: 'keyword' },
              sizes: { type: 'keyword' },
              customPackaging: { type: 'boolean' }
            }
          }
        }
      },
      // Enhanced attributes for better search and categorization
      attributes: {
        properties: {
          images: { type: 'keyword' },
          tags: { type: 'keyword' },
          cropCategory: { type: 'keyword' },
          subCategory: { type: 'keyword' },
          farmingMethod: { type: 'keyword' },
          irrigationMethod: { type: 'keyword' },
          harvestSeason: { type: 'keyword' },
          pestDiseaseStatus: { type: 'keyword' },
          storageMethod: { type: 'keyword' },
          nutrientManagement: { type: 'keyword' },
          waterSource: { type: 'keyword' },
          pesticideUsage: { type: 'keyword' },
          seedType: { type: 'keyword' },
          harvestingMethod: { type: 'keyword' },
          climateZone: { type: 'keyword' },
          altitude: { type: 'integer' },

          soilConditions: {
            properties: {
              type: { type: 'keyword' },
              ph: { type: 'float' },
              nutrients: { type: 'keyword' },
              organicMatter: { type: 'float' },
              drainage: { type: 'keyword' },
              salinity: { type: 'keyword' }
            }
          },
          waterAvailability: { type: 'keyword' },

          nutritionalInfo: {
            properties: {
              calories: { type: 'integer' },
              protein: { type: 'float' },
              carbs: { type: 'float' },
              fiber: { type: 'float' },
              vitamins: { type: 'keyword' },
              minerals: { type: 'keyword' },
              antioxidants: { type: 'keyword' }
            }
          },

          cultivation: {
            properties: {
              irrigationNeeds: { type: 'keyword' },
              fertilizerRequirements: { type: 'keyword' },
              pestControl: { type: 'keyword' },
              climateConditions: { type: 'keyword' },
              sowingMethod: { type: 'keyword' },
              spacingRequirements: { type: 'keyword' },
              companionCrops: { type: 'keyword' }
            }
          },

          postHarvest: {
            properties: {
              storageRequirements: { type: 'keyword' },
              shelfLife: { type: 'keyword' },
              processingMethods: { type: 'keyword' },
              valueAddedProducts: { type: 'keyword' },
              marketDemand: { type: 'keyword' },
              exportPotential: { type: 'boolean' }
            }
          },

          sustainability: {
            properties: {
              waterUsage: { type: 'keyword' },
              carbonFootprint: { type: 'keyword' },
              pesticide: { type: 'keyword' },
              biodiversityImpact: { type: 'keyword' },
              soilHealth: { type: 'keyword' },
              renewableEnergy: { type: 'boolean' }
            }
          },

          market: {
            properties: {
              demandLevel: { type: 'keyword' },
              priceVolatility: { type: 'keyword' },
              seasonalDemand: { type: 'keyword' },
              targetMarkets: { type: 'keyword' },
              competitionLevel: { type: 'keyword' }
            }
          },

          risks: {
            properties: {
              weatherRisks: { type: 'keyword' },
              pestRisks: { type: 'keyword' },
              marketRisks: { type: 'keyword' },
              mitigationStrategies: { type: 'keyword' }
            }
          }
        }
      },
      maintenance: {
        properties: {
          schedule: {
            properties: {
              irrigation: { type: 'date' },
              fertilization: { type: 'date' },
              pestControl: { type: 'date' },
              inspection: { type: 'date' }
            }
          },
          history: {
            properties: {
              activities: {
                properties: {
                  type: { type: 'keyword' },
                  date: { type: 'date' },
                  description: { type: 'text' },
                  performedBy: { type: 'keyword' }
                }
              }
            }
          }
        }
      },
      weather: {
        properties: {
          forecasts: {
            properties: {
              date: { type: 'date' },
              temperature: {
                properties: {
                  min: { type: 'float' },
                  max: { type: 'float' },
                  unit: { type: 'keyword' }
                }
              },
              precipitation: {
                properties: {
                  amount: { type: 'float' },
                  unit: { type: 'keyword' },
                  type: { type: 'keyword' }
                }
              },
              humidity: { type: 'float' },
              windSpeed: {
                properties: {
                  value: { type: 'float' },
                  unit: { type: 'keyword' }
                }
              }
            }
          },
          alerts: {
            properties: {
              type: { type: 'keyword' },
              severity: { type: 'keyword' },
              description: { type: 'text' },
              startDate: { type: 'date' },
              endDate: { type: 'date' }
            }
          }
        }
      },

      // Certifications and compliance
      certifications: { type: 'keyword' },
      compliance: {
        properties: {
          organicCertified: { type: 'boolean' },
          fairTradeCertified: { type: 'boolean' },
          globalGAPCertified: { type: 'boolean' },
          otherCertifications: { type: 'keyword' },
          lastAuditDate: { type: 'date' },
          nextAuditDate: { type: 'date' }
        }
      },

      // Search and discovery metadata
      searchMetadata: {
        properties: {
          searchKeywords: { type: 'keyword' },
          popularityScore: { type: 'float' },
          seasonalRelevance: { type: 'float' },
          qualityScore: { type: 'float' },
          lastSearched: { type: 'date' },
          searchCount: { type: 'integer' }
        }
      },

      // Timestamps
      createdAt: { type: 'date' },
      updatedAt: { type: 'date' },
      lastSyncedAt: { type: 'date' }
    }
  }
};

/**
 * Create index configuration for crops with enhanced search capabilities
 */
export const cropIndexConfig = {
  settings: {
    number_of_shards: 3,
    number_of_replicas: 1,
    analysis: {
      analyzer: {
        crop_analyzer: {
          type: 'custom',
          tokenizer: 'standard',
          filter: ['lowercase', 'asciifolding', 'stop', 'stemmer']
        },
        crop_search_analyzer: {
          type: 'custom',
          tokenizer: 'keyword',
          filter: ['lowercase', 'asciifolding']
        },
        location_analyzer: {
          type: 'custom',
          tokenizer: 'standard',
          filter: ['lowercase', 'asciifolding']
        }
      },
      filter: {
        stemmer: {
          type: 'stemmer',
          language: 'english'
        }
      }
    },
    max_result_window: 50000 // Allow larger result sets for comprehensive searches
  },
  mappings: cropMapping.mappings
};

/**
 * Specialized search configurations for different use cases
 */
export const cropSearchConfigs = {
  // Configuration for product marketplace search
  productSearch: {
    fields: [
      'name^3',
      'type^2.5',
      'variety^2',
      'attributes.cropCategory^2',
      'attributes.subCategory^1.5',
      'attributes.tags^1.5',
      'seller.name^1.2',
      'farm.name^1.2',
      'attributes.nutritionalInfo.vitamins',
      'attributes.market.targetMarkets'
    ],
    filters: [
      'product.isForSale',
      'product.availability.status',
      'seller.location.state',
      'seller.location.city',
      'attributes.cropCategory',
      'attributes.farmingMethod',
      'growthStage',
      'product.qualityGrade'
    ]
  },

  // Configuration for location-based search
  locationSearch: {
    fields: [
      'name^2',
      'type^1.5',
      'seller.location.city^2',
      'seller.location.state^1.5',
      'farm.location.city^2',
      'farm.location.state^1.5'
    ],
    geoFields: [
      'seller.location.coordinates',
      'farm.location.coordinates'
    ]
  },

  // Configuration for seasonal/harvest search
  seasonalSearch: {
    fields: [
      'name^2',
      'type^1.5',
      'attributes.harvestSeason^3',
      'attributes.cropCategory^2'
    ],
    dateFields: [
      'expectedHarvestDate',
      'plantingDate'
    ]
  }
};