/**
 * Search query interface
 */
export interface SearchQuery {
  query?: string;
  filters?: {
    [key: string]: any;
  };
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
  pagination?: {
    page: number;
    size: number;
  };
  aggregations?: {
    [key: string]: {
      type: 'terms' | 'range' | 'date_histogram';
      field: string;
      options?: {
        size?: number;
        ranges?: {
          from?: number;
          to?: number;
        }[];
        interval?: string;
        format?: string;
      };
    };
  };
}

/**
 * Search results interface
 */
export interface SearchResults<T> {
  hits: {
    total: {
      value: number;
      relation: string;
    };
    hits: Array<{
      _id: string;
      _source: T;
      _score: number;
    }>;
  };
  aggregations?: {
    [key: string]: any;
  };
  pagination: {
    page: number;
    size: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Build Elasticsearch query from search query
 * @param searchQuery The search query to build from
 * @returns Elasticsearch query DSL
 */
export function buildElasticsearchQuery(searchQuery: SearchQuery): any {
  const esQuery: any = {
    query: {
      bool: {
        must: [],
        filter: [],
      },
    },
    sort: [],
  };

  // Add text search query if provided
  if (searchQuery.query) {
    esQuery.query.bool.must.push({
      multi_match: {
        query: searchQuery.query,
        fields: ['name^3', 'description^2', 'category', 'attributes.tags'],
        type: 'best_fields',
        fuzziness: 'AUTO',
      },
    });
  }

  // Add filters if provided
  if (searchQuery.filters) {
    Object.entries(searchQuery.filters).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        // Handle array values (terms filter)
        esQuery.query.bool.filter.push({
          terms: {
            [key]: value,
          },
        });
      } else if (typeof value === 'object' && value !== null) {
        // Handle range filters
        const rangeFilter: any = {};
        if ('gte' in value || 'gt' in value || 'lte' in value || 'lt' in value) {
          rangeFilter[key] = {};
          if ('gte' in value) rangeFilter[key].gte = value.gte;
          if ('gt' in value) rangeFilter[key].gt = value.gt;
          if ('lte' in value) rangeFilter[key].lte = value.lte;
          if ('lt' in value) rangeFilter[key].lt = value.lt;
          esQuery.query.bool.filter.push({
            range: rangeFilter,
          });
        }
      } else {
        // Handle simple term filters
        esQuery.query.bool.filter.push({
          term: {
            [key]: value,
          },
        });
      }
    });
  }

  // Add sorting if provided
  if (searchQuery.sort) {
    esQuery.sort.push({
      [searchQuery.sort.field]: {
        order: searchQuery.sort.order,
      },
    });
  } else {
    // Default sort by _score
    esQuery.sort.push({ _score: { order: 'desc' } });
  }

  // Add pagination if provided
  if (searchQuery.pagination) {
    const { page, size } = searchQuery.pagination;
    esQuery.from = (page - 1) * size;
    esQuery.size = size;
  } else {
    // Default pagination
    esQuery.from = 0;
    esQuery.size = 10;
  }

  // Add aggregations if provided
  if (searchQuery.aggregations) {
    esQuery.aggs = {};
    Object.entries(searchQuery.aggregations).forEach(([name, config]) => {
      switch (config.type) {
        case 'terms':
          esQuery.aggs[name] = {
            terms: {
              field: config.field,
              size: config.options?.size || 10,
            },
          };
          break;
        case 'range':
          if (config.options?.ranges) {
            esQuery.aggs[name] = {
              range: {
                field: config.field,
                ranges: config.options.ranges,
              },
            };
          }
          break;
        case 'date_histogram':
          if (config.options?.interval) {
            esQuery.aggs[name] = {
              date_histogram: {
                field: config.field,
                calendar_interval: config.options.interval,
                format: config.options?.format || 'yyyy-MM-dd',
              },
            };
          }
          break;
      }
    });
  }

  return esQuery;
}

/**
 * Transform Elasticsearch response to SearchResults
 * @param esResponse The response from Elasticsearch
 * @param page Current page number
 * @param size Page size
 * @returns Formatted search results
 */
export function transformElasticsearchResponse<T>(
  esResponse: any,
  page: number = 1,
  size: number = 10
): SearchResults<T> {
  return {
    hits: {
      total: esResponse.hits.total,
      hits: esResponse.hits.hits.map((hit: any) => ({
        _id: hit._id,
        _source: hit._source,
        _score: hit._score,
      })),
    },
    aggregations: esResponse.aggregations,
    pagination: {
      page,
      size,
      total: esResponse.hits.total.value,
      totalPages: Math.ceil(esResponse.hits.total.value / size),
    },
  };
} 