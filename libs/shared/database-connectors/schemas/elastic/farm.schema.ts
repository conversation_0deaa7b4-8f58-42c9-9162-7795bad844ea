/**
 * Farm document interface for Elasticsearch
 */
export interface FarmDocument {
  id: string;
  farmId: string;
  sellerId: string;
  name: string;
  location: {
    country: string;
    state: string;
    city: string;
    pincode: string;
    addressLine1: string;
    addressLine2: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  totalArea: number;
  soilType: string;
  waterSource: string;
  infrastructure: string[];
  certifications: string[];
  status: 'ACTIVE' | 'INACTIVE';

  // Enhanced farm management for search
  currentCrops: [{
    cropId: string;
    plotId: string;
    name: string;
    variety: string;
    plantingDate: string; // ISO date string
    expectedHarvestDate: string; // ISO date string
    growthStage: string;
    season: 'KHARIF' | 'RABI' | 'ZAID';
  }];

  cropRotationPlan: [{
    plotId: string;
    season: 'KHARIF' | 'RABI' | 'ZAID';
    year: number;
    plannedCrop: string;
    variety?: string;
    status: 'PLANNED' | 'PLANTED' | 'HARVESTED';
  }];

  farmingPractices: {
    primaryMethod: 'ORGANIC' | 'CONVENTIONAL' | 'INTEGRATED';
    irrigationSystems: string[];
    sustainabilityScore?: number;
  };

  // Denormalized seller information for better search
  seller: {
    sellerId: string;
    name: string;
    contact: string;
    email: string;
    location: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2: string;
    };
    certifications: string[];
    experienceYears?: number;
  };

  // Search and analytics metadata
  searchMetadata: {
    cropTypes: string[]; // All crop types grown on this farm
    seasons: string[]; // Active seasons
    totalPlots: number;
    averageYield?: number;
    sustainabilityRating?: number;
    popularityScore: number;
  };

  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

/**
 * Farm mapping for Elasticsearch
 */
export const farmMapping = {
  mappings: {
    properties: {
      id: { type: 'keyword' },
      farmId: { type: 'keyword' },
      sellerId: { type: 'keyword' },
      name: {
        type: 'text',
        analyzer: 'farm_analyzer',
        fields: {
          keyword: { type: 'keyword' },
          suggest: {
            type: 'completion',
            analyzer: 'simple'
          }
        }
      },
      location: {
        properties: {
          country: { type: 'keyword' },
          state: { type: 'keyword' },
          city: { type: 'keyword' },
          pincode: { type: 'keyword' },
          addressLine1: { type: 'text' },
          addressLine2: { type: 'text' },
          coordinates: {
            properties: {
              latitude: { type: 'float' },
              longitude: { type: 'float' }
            }
          }
        }
      },
      totalArea: { type: 'float' },
      soilType: { type: 'keyword' },
      waterSource: { type: 'keyword' },
      infrastructure: { type: 'keyword' },
      certifications: { type: 'keyword' },
      status: { type: 'keyword' },

      // Enhanced farm management fields
      currentCrops: {
        type: 'nested',
        properties: {
          cropId: { type: 'keyword' },
          plotId: { type: 'keyword' },
          name: {
            type: 'text',
            analyzer: 'farm_analyzer',
            fields: { keyword: { type: 'keyword' } }
          },
          variety: { type: 'keyword' },
          plantingDate: { type: 'date' },
          expectedHarvestDate: { type: 'date' },
          growthStage: { type: 'keyword' },
          season: { type: 'keyword' }
        }
      },

      cropRotationPlan: {
        type: 'nested',
        properties: {
          plotId: { type: 'keyword' },
          season: { type: 'keyword' },
          year: { type: 'integer' },
          plannedCrop: {
            type: 'text',
            analyzer: 'farm_analyzer',
            fields: { keyword: { type: 'keyword' } }
          },
          variety: { type: 'keyword' },
          status: { type: 'keyword' }
        }
      },

      farmingPractices: {
        properties: {
          primaryMethod: { type: 'keyword' },
          irrigationSystems: { type: 'keyword' },
          sustainabilityScore: { type: 'float' }
        }
      },

      // Denormalized seller information
      seller: {
        properties: {
          sellerId: { type: 'keyword' },
          name: {
            type: 'text',
            analyzer: 'farm_analyzer',
            fields: { keyword: { type: 'keyword' } }
          },
          contact: { type: 'keyword' },
          email: { type: 'keyword' },
          location: {
            properties: {
              country: { type: 'keyword' },
              state: { type: 'keyword' },
              city: { type: 'keyword' },
              pincode: { type: 'keyword' },
              addressLine1: { type: 'text' },
              addressLine2: { type: 'text' }
            }
          },
          certifications: { type: 'keyword' },
          experienceYears: { type: 'integer' }
        }
      },

      // Search and analytics metadata
      searchMetadata: {
        properties: {
          cropTypes: { type: 'keyword' },
          seasons: { type: 'keyword' },
          totalPlots: { type: 'integer' },
          averageYield: { type: 'float' },
          sustainabilityRating: { type: 'float' },
          popularityScore: { type: 'float' }
        }
      },

      createdAt: { type: 'date' },
      updatedAt: { type: 'date' }
    }
  }
};

/**
 * Create index configuration for farms
 */
export const farmIndexConfig = {
  settings: {
    number_of_shards: 3,
    number_of_replicas: 1,
    analysis: {
      analyzer: {
        farm_analyzer: {
          type: 'custom',
          tokenizer: 'standard',
          filter: ['lowercase', 'asciifolding']
        }
      }
    }
  },
  mappings: farmMapping.mappings
}; 