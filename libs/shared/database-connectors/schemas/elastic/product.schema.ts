/**
 * Product types enum
 */
export enum ProductType {
  PLOT = 'PLOT',
  CROP = 'CROP',
  FARM_SERVICE = 'FARM_SERVICE',
  EQUIPMENT = 'EQUIPMENT',
  SUPPLY = 'SUPPLY',
}

/**
 * Crop quality grade enum
 */
export enum CropQualityGrade {
  PREMIUM = 'PREMIUM',
  STANDARD = 'STANDARD',
  ECONOMY = 'ECONOMY',
}

/**
 * Product document interface for Elasticsearch
 */
export interface ProductDocument {
  id: string;
  type: ProductType;
  sellerId: string;
  name: string;
  description: string;
  category: string;
  subCategory?: string;
  price: {
    amount: number;
    currency: string;
    unit?: string; // per kg, per acre, etc.
  };
  availability: {
    status: 'AVAILABLE' | 'OUT_OF_STOCK' | 'LIMITED';
    quantity?: number;
  };
  attributes: {
    // Common attributes
    images?: string[];
    tags?: string[];
    rating?: number;
    
    // Plot-specific attributes
    size?: number;
    location?: {
      country: string;
      state: string;
      city: string;
      pincode: string;
      addressLine1: string;
      addressLine2: string;
      latitude?: number;
      longitude?: number;
    };
    soilType?: string;
    waterAvailability?: string;
    
    // Crop-specific attributes
    cropCategory?: string;  // Vegetables, Fruits, etc.
    variety?: string;
    farmingMethod?: string; // Organic, Conventional, etc.
    growthStage?: string;   // Seedling, Vegetative Growth, etc.
    irrigationMethod?: string; // Rain-fed, Canal, etc.
    harvestSeason?: string; // Kharif, Rabi, Zaid
    ownershipUnit?: string; // Percentage, Area in sq meters, etc.
    pestDiseaseStatus?: string; // None, Minor Infestation, etc.
    storageMethod?: string; // Open Storage, Cold Storage, etc.
    nutrientManagement?: string; // Organic Fertilizers, etc.
    waterSource?: string;   // Rainwater Harvesting, Groundwater, etc.
    pesticideUsage?: string; // None, Organic Pesticides, etc.
    seedType?: string;      // Heirloom, Hybrid, GMO
    cropHealthStatus?: string; // Excellent, Good, Fair, etc.
    harvestingMethod?: string; // Manual, Mechanical
    packagingType?: string;
    transportationOptions?: string[];
    estimatedDeliveryTime?: string;
    deliveryCharges?: number;
    fulfillmentStatus?: string; // Pending Harvest, Ready for Pickup/Delivery, etc.
    riskFactors?: string[];
    
    growingSeason?: string;
    harvestPeriod?: string;
    expectedYield?: number;
    yieldUnit?: string;
    minimumOwnershipUnit?: number;
    totalAvailableQuantity?: number;
    harvestDateRange?: {
      startDate?: string;
      endDate?: string;
    };
    qualityGrade?: CropQualityGrade;
    nutritionalInfo?: {
      calories?: number;
      protein?: number;
      carbs?: number;
      fiber?: number;
      vitamins?: string[];
    };
    cultivation?: {
      irrigationNeeds?: string;
      fertilizerRequirements?: string;
      pestControl?: string;
      climateConditions?: string;
    };
    postHarvest?: {
      storageRequirements?: string;
      shelfLife?: string;
      processingMethods?: string[];
    };
    sustainability?: {
      waterUsage?: string;
      carbonFootprint?: string;
      pesticide?: string;
    };
    
    // Farmer-related attributes
    farmerExperienceLevel?: string; // Beginner, Intermediate, Experienced
    farmerCertification?: string;
    farmerReputationScore?: number;
    
    // Transaction-related attributes
    ownershipStatus?: string; // Available, Partially Owned, etc.
    paymentMethods?: string[]; // Credit Card, Debit Card, etc.
    pricePerOwnershipUnit?: number;
    paymentTerms?: string;
    cancellationPolicy?: string;
    agreementDocumentUrl?: string;
    taxInformation?: string;
    
    // Platform usage
    listingCreationDate?: string;
    listingExpiryDate?: string;
    viewCount?: number;
    wishlistCount?: number;
    
    farmId?: string;
    plotId?: string;
    
    // Farm service-specific attributes
    serviceArea?: string;
    serviceDuration?: string;
    equipmentUsed?: string[];
    
    // Equipment/Supply specific attributes
    brand?: string;
    model?: string;
    condition?: string;
    expiryDate?: string;
  };
  certifications?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Product mapping for Elasticsearch
 */
export const productMapping = {
  mappings: {
    properties: {
      id: { type: 'keyword' },
      type: { type: 'keyword' },
      sellerId: { type: 'keyword' },
      name: { 
        type: 'text',
        fields: {
          keyword: { type: 'keyword' }
        }
      },
      description: { type: 'text' },
      category: { type: 'keyword' },
      subCategory: { type: 'keyword' },
      price: {
        properties: {
          amount: { type: 'double' },
          currency: { type: 'keyword' },
          unit: { type: 'keyword' }
        }
      },
      availability: {
        properties: {
          status: { type: 'keyword' },
          quantity: { type: 'integer' }
        }
      },
      attributes: {
        properties: {
          images: { type: 'keyword' },
          tags: { type: 'keyword' },
          rating: { type: 'float' },
          size: { type: 'double' },
          location: {
            properties: {
              country: { type: 'keyword', default: 'INDIA' },
              state: { type: 'keyword' },
              city: { type: 'keyword' },
              pincode: { type: 'keyword' },
              addressLine1: { type: 'text' },
              addressLine2: { type: 'text' },
              latitude: { type: 'double' },
              longitude: { type: 'double' }
            }
          },
          soilType: { type: 'keyword' },
          waterAvailability: { type: 'keyword' },
          
          // Enhanced crop-specific attributes
          cropCategory: { type: 'keyword' },
          variety: { type: 'keyword' },
          farmingMethod: { type: 'keyword' },
          growthStage: { type: 'keyword' },
          irrigationMethod: { type: 'keyword' },
          harvestSeason: { type: 'keyword' },
          ownershipUnit: { type: 'keyword' },
          pestDiseaseStatus: { type: 'keyword' },
          storageMethod: { type: 'keyword' },
          nutrientManagement: { type: 'keyword' },
          waterSource: { type: 'keyword' },
          pesticideUsage: { type: 'keyword' },
          seedType: { type: 'keyword' },
          cropHealthStatus: { type: 'keyword' },
          harvestingMethod: { type: 'keyword' },
          packagingType: { type: 'keyword' },
          transportationOptions: { type: 'keyword' },
          estimatedDeliveryTime: { type: 'keyword' },
          deliveryCharges: { type: 'double' },
          fulfillmentStatus: { type: 'keyword' },
          riskFactors: { type: 'keyword' },
          
          growingSeason: { type: 'keyword' },
          harvestPeriod: { type: 'keyword' },
          expectedYield: { type: 'double' },
          yieldUnit: { type: 'keyword' },
          minimumOwnershipUnit: { type: 'double' },
          totalAvailableQuantity: { type: 'double' },
          harvestDateRange: {
            properties: {
              startDate: { type: 'date' },
              endDate: { type: 'date' }
            }
          },
          qualityGrade: { type: 'keyword' },
          nutritionalInfo: {
            properties: {
              calories: { type: 'integer' },
              protein: { type: 'float' },
              carbs: { type: 'float' },
              fiber: { type: 'float' },
              vitamins: { type: 'keyword' }
            }
          },
          cultivation: {
            properties: {
              irrigationNeeds: { type: 'keyword' },
              fertilizerRequirements: { type: 'keyword' },
              pestControl: { type: 'keyword' },
              climateConditions: { type: 'keyword' }
            }
          },
          postHarvest: {
            properties: {
              storageRequirements: { type: 'keyword' },
              shelfLife: { type: 'keyword' },
              processingMethods: { type: 'keyword' }
            }
          },
          sustainability: {
            properties: {
              waterUsage: { type: 'keyword' },
              carbonFootprint: { type: 'keyword' },
              pesticide: { type: 'keyword' }
            }
          },
          
          // Farmer-related attributes
          farmerExperienceLevel: { type: 'keyword' },
          farmerCertification: { type: 'keyword' },
          farmerReputationScore: { type: 'float' },
          
          // Transaction-related attributes
          ownershipStatus: { type: 'keyword' },
          paymentMethods: { type: 'keyword' },
          pricePerOwnershipUnit: { type: 'double' },
          paymentTerms: { type: 'keyword' },
          cancellationPolicy: { type: 'keyword' },
          agreementDocumentUrl: { type: 'keyword' },
          taxInformation: { type: 'keyword' },
          
          // Platform usage
          listingCreationDate: { type: 'date' },
          listingExpiryDate: { type: 'date' },
          viewCount: { type: 'integer' },
          wishlistCount: { type: 'integer' },
          
          farmId: { type: 'keyword' },
          plotId: { type: 'keyword' },
          
          serviceArea: { type: 'keyword' },
          serviceDuration: { type: 'keyword' },
          equipmentUsed: { type: 'keyword' },
          brand: { type: 'keyword' },
          model: { type: 'keyword' },
          condition: { type: 'keyword' },
          expiryDate: { type: 'date' }
        }
      },
      certifications: { type: 'keyword' },
      createdAt: { type: 'date' },
      updatedAt: { type: 'date' }
    }
  }
};

/**
 * Create index configuration for products
 */
export const productIndexConfig = {
  settings: {
    number_of_shards: 3,
    number_of_replicas: 1,
    analysis: {
      analyzer: {
        product_analyzer: {
          type: 'custom',
          tokenizer: 'standard',
          filter: ['lowercase', 'asciifolding']
        }
      }
    }
  },
  mappings: productMapping.mappings
}; 