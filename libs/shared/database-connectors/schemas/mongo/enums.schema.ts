import mongoose, { Schema, Document } from 'mongoose';

/**
 * Enum category type
 */
export enum EnumCategoryType {
  CROP = 'CROP',
  LOCATION = 'LOCATION',
  FARMER = 'FARMER',
  TRANSACTION = 'TRANSACTION',
  PLATFORM = 'PLATFORM'
}

/**
 * Enum document interface
 */
export interface EnumDocument extends Document {
  name: string;
  category: EnumCategoryType;
  subcategory: string;
  values: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Enum schema definition
 */
const EnumSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      enum: Object.values(EnumCategoryType),
      required: true,
    },
    subcategory: {
      type: String,
      required: true,
    },
    values: [{
      type: String,
      required: true,
    }],
    isActive: {
      type: Boolean,
      default: true,
    }
  },
  {
    timestamps: true,
  }
);

// Create a compound index on category and subcategory to ensure uniqueness
EnumSchema.index({ category: 1, subcategory: 1 }, { unique: true });

// Create and export the Enum model
export const EnumModel = mongoose.model<EnumDocument>('Enum', EnumSchema); 