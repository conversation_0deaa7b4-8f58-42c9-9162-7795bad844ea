import mongoose, { Schema, Document } from 'mongoose';

/**
 * Order document interface
 */
export interface OrderDocument extends Document {
  orderId: string;
  farmerId: string;
  buyerId: string;
  plotId: string;
  orderDetails: {
    plotSize: number;
    cropType: string;
    price: number;
    currency: string;
  };
  status: 'PENDING' | 'CONFIRMED' | 'PROCESSING' | 'COMPLETED' | 'CANCELLED';
  timeline: {
    orderDate: Date;
    confirmationDate?: Date;
    processingDate?: Date;
    completionDate?: Date;
  };
  payment: {
    status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
    amount: number;
    transactionId?: string;
    paymentMethod: string;
  };
  communication: {
    notifications: string[];
    messages: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Order schema definition
 */
const OrderSchema = new Schema(
  {
    orderId: {
      type: String,
      required: true,
      unique: true,
    },
    farmerId: {
      type: String,
      required: true,
      ref: 'Farmer',
    },
    buyerId: {
      type: String,
      required: true,
    },
    plotId: {
      type: String,
      required: true,
      ref: 'Plot',
    },
    orderDetails: {
      plotSize: {
        type: Number,
        required: true,
      },
      cropType: {
        type: String,
        required: true,
      },
      price: {
        type: Number,
        required: true,
      },
      currency: {
        type: String,
        required: true,
        default: 'USD',
      },
    },
    status: {
      type: String,
      enum: ['PENDING', 'CONFIRMED', 'PROCESSING', 'COMPLETED', 'CANCELLED'],
      default: 'PENDING',
    },
    timeline: {
      orderDate: {
        type: Date,
        default: Date.now,
      },
      confirmationDate: {
        type: Date,
      },
      processingDate: {
        type: Date,
      },
      completionDate: {
        type: Date,
      },
    },
    payment: {
      status: {
        type: String,
        enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'],
        default: 'PENDING',
      },
      amount: {
        type: Number,
        required: true,
      },
      transactionId: {
        type: String,
      },
      paymentMethod: {
        type: String,
        required: true,
      },
    },
    communication: {
      notifications: [
        {
          type: String,
        },
      ],
      messages: [
        {
          type: String,
        },
      ],
    },
  },
  {
    timestamps: true,
  }
);

// Create and export the Order model
export const OrderModel = mongoose.model<OrderDocument>('Order', OrderSchema); 