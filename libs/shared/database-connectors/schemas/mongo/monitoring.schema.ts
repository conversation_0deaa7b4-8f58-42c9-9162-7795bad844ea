import mongoose, { Schema, Document } from 'mongoose';

/**
 * Monitoring document interface
 */
export interface MonitoringDocument extends Document {
  monitoringId: string;
  type: 'SYSTEM' | 'PERFORMANCE' | 'SECURITY' | 'USER';
  metrics: {
    name: string;
    value: number;
    threshold: number;
    status: 'NORMAL' | 'WARNING' | 'CRITICAL';
  };
  alerts: [{
    alertId: string;
    type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH';
    status: 'ACTIVE' | 'RESOLVED';
    timestamp: Date;
  }];
  actions: [{
    actionId: string;
    type: string;
    status: 'PENDING' | 'COMPLETED' | 'FAILED';
    timestamp: Date;
  }];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Monitoring schema definition
 */
const MonitoringSchema = new Schema(
  {
    monitoringId: {
      type: String,
      required: true,
      unique: true,
    },
    type: {
      type: String,
      enum: ['SYSTEM', 'PERFORMANCE', 'SECURITY', 'USER'],
      required: true,
    },
    metrics: {
      name: {
        type: String,
        required: true,
      },
      value: {
        type: Number,
        required: true,
      },
      threshold: {
        type: Number,
        required: true,
      },
      status: {
        type: String,
        enum: ['NORMAL', 'WARNING', 'CRITICAL'],
        default: 'NORMAL',
      },
    },
    alerts: [{
      alertId: {
        type: String,
        required: true,
      },
      type: {
        type: String,
        required: true,
      },
      severity: {
        type: String,
        enum: ['LOW', 'MEDIUM', 'HIGH'],
        required: true,
      },
      status: {
        type: String,
        enum: ['ACTIVE', 'RESOLVED'],
        default: 'ACTIVE',
      },
      timestamp: {
        type: Date,
        default: Date.now,
      },
    }],
    actions: [{
      actionId: {
        type: String,
        required: true,
      },
      type: {
        type: String,
        required: true,
      },
      status: {
        type: String,
        enum: ['PENDING', 'COMPLETED', 'FAILED'],
        default: 'PENDING',
      },
      timestamp: {
        type: Date,
        default: Date.now,
      },
    }],
  },
  {
    timestamps: true,
  }
);

// Create and export the Monitoring model
export const MonitoringModel = mongoose.model<MonitoringDocument>('Monitoring', MonitoringSchema); 