import mongoose, { Schema, Document } from 'mongoose';

/**
 * Admin document interface
 */
export interface AdminDocument extends Document {
  adminId: string;
  personalInfo: {
    name: string;
    email: string;
    role: 'SUPER_ADMIN' | 'ADMIN' | 'SUPPORT';
    permissions: string[];
  };
  activity: {
    lastLogin: Date;
    actions: [{
      actionId: string;
      type: string;
      timestamp: Date;
      details: string;
    }];
  };
  preferences: {
    notifications: boolean;
    dashboard: any;
    reports: any;
  };
  createdAt: Date;
  updatedAt: Date;
  password: string;
}

/**
 * Admin schema definition
 */
const AdminSchema = new Schema(
  {
    adminId: {
      type: String,
      required: true,
      unique: true,
    },
    personalInfo: {
      name: {
        type: String,
        required: true,
      },
      email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
      },
      role: {
        type: String,
        enum: ['SUPER_ADMIN', 'ADMIN', 'SUPPORT'],
        required: true,
      },
      permissions: [{
        type: String,
      }],
    },
    activity: {
      lastLogin: {
        type: Date,
      },
      actions: [{
        actionId: {
          type: String,
        },
        type: {
          type: String,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        details: {
          type: String,
        },
      }],
    },
    preferences: {
      notifications: {
        type: Boolean,
        default: true,
      },
      dashboard: {
        type: Schema.Types.Mixed,
      },
      reports: {
        type: Schema.Types.Mixed,
      },
    },
    password: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Create and export the Admin model
export const AdminModel = mongoose.model<AdminDocument>('Admin', AdminSchema); 