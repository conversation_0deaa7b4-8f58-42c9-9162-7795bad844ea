import { AdminModel } from '../schemas/mongo/admin.schema';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import * as bcrypt from 'bcrypt';

/**
 * Hash a password using bcrypt
 */
const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, 10);
};

/**
 * Seed data for admin users with bcrypted passwords
 *
 * Login Credentials:
 * - Super Admin: <EMAIL> / superadmin123
 * - Admin User: <EMAIL> / admin123
 * - Support Staff: <EMAIL> / support123
 */
export const adminSeeds = [
  {
    adminId: uuidv4(),
    personalInfo: {
      name: 'Super Admin',
      email: '<EMAIL>',
      role: 'SUPER_ADMIN',
      permissions: [
        'all:create', 'all:read', 'all:update', 'all:delete',
        'user:manage', 'farm:manage', 'crop:manage', 'order:manage',
        'report:view', 'system:configure'
      ],
    },
    activity: {
      lastLogin: new Date(),
      actions: [{
        actionId: uuidv4(),
        type: 'SYSTEM',
        timestamp: new Date(),
        details: 'Account created during system initialization',
      }],
    },
    preferences: {
      notifications: true,
      dashboard: { defaultView: 'summary' },
      reports: { defaultFormat: 'dashboard' },
    },
    // Password: superadmin123
    password: '$2b$10$v2zOiJQj7gyiaBle/mh.duAJkieeHxSWwjL8Tkrv41DELONvdTdO6'
  },
  {
    adminId: uuidv4(),
    personalInfo: {
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
      permissions: [
        'farm:create', 'farm:read', 'farm:update',
        'crop:create', 'crop:read', 'crop:update',
        'order:read', 'order:update',
        'report:view'
      ],
    },
    activity: {
      lastLogin: new Date(),
      actions: [{
        actionId: uuidv4(),
        type: 'SYSTEM',
        timestamp: new Date(),
        details: 'Account created during system initialization',
      }],
    },
    preferences: {
      notifications: true,
      dashboard: { defaultView: 'farms' },
      reports: { defaultFormat: 'list' },
    },
    // Password: admin123
    password: '$2b$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW'
  },
  {
    adminId: uuidv4(),
    personalInfo: {
      name: 'Support Staff',
      email: '<EMAIL>',
      role: 'SUPPORT',
      permissions: [
        'farm:read',
        'crop:read',
        'order:read',
        'user:read'
      ],
    },
    activity: {
      lastLogin: new Date(),
      actions: [{
        actionId: uuidv4(),
        type: 'SYSTEM',
        timestamp: new Date(),
        details: 'Account created during system initialization',
      }],
    },
    preferences: {
      notifications: true,
      dashboard: { defaultView: 'support' },
      reports: { defaultFormat: 'list' },
    },
    // Password: support123
    password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
  }
];

/**
 * Seed admin users in the database with bcrypted passwords
 */
export const seedAdmins = async (): Promise<void> => {
  try {
    // Clear existing admin users
    await AdminModel.deleteMany({});
    console.log('Existing admin users cleared successfully.');

    // Insert new admin users with bcrypted passwords
    await AdminModel.insertMany(adminSeeds);
    console.log(`Successfully seeded ${adminSeeds.length} admin users with encrypted passwords.`);

    // Log admin credentials for reference
    console.log('🔐 Admin Login Credentials:');
    console.log('  <EMAIL> / superadmin123 (SUPER_ADMIN)');
    console.log('  <EMAIL> / admin123 (ADMIN)');
    console.log('  <EMAIL> / support123 (SUPPORT)');
  } catch (error) {
    console.error('Error seeding admin users:', error);
    throw error;
  }
};