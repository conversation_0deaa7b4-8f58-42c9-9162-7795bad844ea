import mongoose from 'mongoose';
import { seedEnums } from './enum-seeds';
import { seedAdmins } from './admin-seeds';
import { seedFarmers, seedFarms } from './farmer-seeds';
import { seedCrops } from './crop-seeds';

/**
 * Available seed types
 */
export enum SeedType {
  ENUMS = 'enums',
  ADMINS = 'admins',
  FARMERS = 'farmers', // This seeds the Seller model with farmer data (farmers are the sellers)
  FARMS = 'farms',
  PLOTS = 'plots',
  CROPS = 'crops'
}

/**
 * Run all seed operations
 * @param seedTypes Optional array of seed types to run. If not provided, all seeds will run.
 */
export const runAllSeeds = async (seedTypes?: string[]): Promise<void> => {
  try {
    // Check if already connected
    if (mongoose.connection.readyState === 0) {
      // Connect to MongoDB
      await mongoose.connect(process.env['MONGODB_URI'] || 'mongodb://localhost:27017/agritech');
      console.log('Connected to MongoDB');
    }
    
    // If seedTypes is not provided or empty, run all seeds
    const runAll = !seedTypes || seedTypes.length === 0;
    
    // Run seed operations based on types provided or all if none provided
    if (runAll || seedTypes.includes(SeedType.ENUMS)) {
      await seedEnums();
    }
    
    if (runAll || seedTypes.includes(SeedType.ADMINS)) {
      await seedAdmins();
    }
    
    if (runAll || seedTypes.includes(SeedType.FARMERS)) {
      await seedFarmers(); // Seeds the Farmer model
    }

    if (runAll || seedTypes.includes(SeedType.FARMS)) {
      await seedFarms(); // Seeds farms and links them to farmers
    }

    // if (runAll || seedTypes.includes(SeedType.PLOTS)) {
    //   await seedPlots();
    // }

    if (runAll || seedTypes.includes(SeedType.CROPS)) {
      await seedCrops(); // Seeds crops with proper farm-crop rotation
    }
    
    console.log('Seed operations completed successfully');
    
    // Close connection only if we opened it
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
      console.log('MongoDB connection closed');
    }
  } catch (error) {
    console.error('Failed to run seeds:', error);
    
    // Close connection on error
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }
    
    throw error;
  }
}; 