import { EnumCategoryType, EnumModel } from '../schemas/mongo/enums.schema';
import mongoose from 'mongoose';

/**
 * Seed data for enum values
 */
export const enumSeeds = [
  // CROP related enums
  {
    name: 'Crop Categories',
    category: EnumCategoryType.CROP,
    subcategory: 'crop_category',
    values: ['Vegetables', 'Fruits', 'Paddy/Rice', 'Wheat', 'Pulses', 'Oilseeds', 'Spices', 'Horticulture/Plantation']
  },
  {
    name: 'Farming Methods',
    category: EnumCategoryType.CROP,
    subcategory: 'farming_method',
    values: ['Organic', 'Conventional', 'Hydroponics', 'Integrated Farming']
  },
  {
    name: 'Growth Stages',
    category: EnumCategoryType.CROP,
    subcategory: 'growth_stage',
    values: ['Seedling', 'Vegetative Growth', 'Flowering', 'Fruiting', 'Ready for Harvest']
  },
  {
    name: 'Soil Types',
    category: EnumCategoryType.CROP,
    subcategory: 'soil_type',
    values: ['Alluvial', 'Black Cotton', 'Red Loam', 'Laterite']
  },
  {
    name: 'Irrigation Methods',
    category: EnumCategoryType.CROP,
    subcategory: 'irrigation_method',
    values: ['Rain-fed', 'Canal', 'Well', 'Drip Irrigation', 'Sprinkler']
  },
  {
    name: 'Harvest Seasons',
    category: EnumCategoryType.CROP,
    subcategory: 'harvest_season',
    values: ['Kharif', 'Rabi', 'Zaid']
  },
  {
    name: 'Ownership Units',
    category: EnumCategoryType.CROP,
    subcategory: 'ownership_unit',
    values: ['Percentage', 'Area in sq meters', 'Estimated Yield in kg', 'Fixed Units']
  },
  {
    name: 'Pest Disease Status',
    category: EnumCategoryType.CROP,
    subcategory: 'pest_disease_status',
    values: ['None', 'Minor Infestation', 'Major Outbreak', 'Under Treatment']
  },
  {
    name: 'Storage Methods',
    category: EnumCategoryType.CROP,
    subcategory: 'storage_method',
    values: ['Open Storage', 'Cold Storage', 'Ventilated Storage', 'Silos']
  },
  {
    name: 'Nutrient Management',
    category: EnumCategoryType.CROP,
    subcategory: 'nutrient_management',
    values: ['Organic Fertilizers', 'Chemical Fertilizers', 'Integrated Nutrient Management', 'Green Manure']
  },
  {
    name: 'Water Sources',
    category: EnumCategoryType.CROP,
    subcategory: 'water_source',
    values: ['Rainwater Harvesting', 'Groundwater', 'Surface Water (Canal, River, Pond)']
  },
  {
    name: 'Pesticide Usage',
    category: EnumCategoryType.CROP,
    subcategory: 'pesticide_usage',
    values: ['None', 'Organic Pesticides', 'Chemical Pesticides', 'Integrated Pest Management']
  },
  {
    name: 'Seed Types',
    category: EnumCategoryType.CROP,
    subcategory: 'seed_type',
    values: ['Heirloom', 'Hybrid', 'GMO']
  },
  {
    name: 'Crop Health Status',
    category: EnumCategoryType.CROP,
    subcategory: 'crop_health_status',
    values: ['Excellent', 'Good', 'Fair', 'Needs Attention']
  },
  {
    name: 'Harvesting Methods',
    category: EnumCategoryType.CROP,
    subcategory: 'harvesting_method',
    values: ['Manual', 'Mechanical']
  },
  {
    name: 'Fulfillment Status',
    category: EnumCategoryType.CROP,
    subcategory: 'fulfillment_status',
    values: ['Pending Harvest', 'Ready for Pickup/Delivery', 'In Transit', 'Delivered']
  },

  // FARMER related enums
  {
    name: 'Experience Levels',
    category: EnumCategoryType.FARMER,
    subcategory: 'experience_level',
    values: ['Beginner', 'Intermediate', 'Experienced']
  },
  {
    name: 'Communication Preferences',
    category: EnumCategoryType.FARMER,
    subcategory: 'communication_preferences',
    values: ['In-app Messaging', 'Email', 'SMS']
  },
  {
    name: 'Updates Frequency Preference',
    category: EnumCategoryType.FARMER,
    subcategory: 'updates_frequency_preference',
    values: ['Daily', 'Weekly', 'Bi-weekly', 'On Significant Events']
  },

  // TRANSACTION related enums
  {
    name: 'Ownership Status',
    category: EnumCategoryType.TRANSACTION,
    subcategory: 'ownership_status',
    values: ['Available', 'Partially Owned', 'Fully Owned', 'Harvested', 'Completed']
  },
  {
    name: 'Payment Methods',
    category: EnumCategoryType.TRANSACTION,
    subcategory: 'payment_method',
    values: ['Credit Card', 'Debit Card', 'Net Banking', 'UPI', 'Wallet']
  },
  {
    name: 'Transaction Status',
    category: EnumCategoryType.TRANSACTION,
    subcategory: 'transaction_status',
    values: ['Pending', 'Successful', 'Failed', 'Refunded']
  }
];

/**
 * Seed enums in the database
 */
export const seedEnums = async (): Promise<void> => {
  try {
    // Clear existing enums
    await EnumModel.deleteMany({});
    console.log('Existing enums cleared successfully.');
    
    // Insert new enum values
    await EnumModel.insertMany(enumSeeds);
    console.log(`Successfully seeded ${enumSeeds.length} enum categories.`);
  } catch (error) {
    console.error('Error seeding enums:', error);
    throw error;
  }
}; 