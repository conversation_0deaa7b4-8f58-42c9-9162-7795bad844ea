{"name": "shared", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared", "main": "libs/shared/index.ts", "tsConfig": "libs/shared/tsconfig.lib.json", "assets": ["libs/shared/*.md"]}}}, "tags": ["type:shared", "scope:shared"]}