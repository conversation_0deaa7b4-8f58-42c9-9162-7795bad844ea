#!/bin/bash

# AgriTech Data Seeding and Authentication Test Script
# This script seeds the database and tests authentication with bcrypted passwords

# Set your API key here (in actual use, this should be set in environment variables)
API_KEY="test-admin-key"

echo "🌱 AgriTech Database Seeding and Authentication Test"
echo "=================================================="

# Test seed all data
echo "📊 Seeding all data with bcrypted passwords..."
curl -X POST http://localhost:3333/api/seed \
  -H "Content-Type: application/json" \
  -d "{\"apiKey\": \"$API_KEY\"}"

echo -e "\n\n"

# Test seed enums only
echo "📋 Testing seed enums only..."
curl -X POST http://localhost:3333/api/seed \
  -H "Content-Type: application/json" \
  -d "{\"apiKey\": \"$API_KEY\", \"seedTypes\": [\"enums\"]}"

echo -e "\n\n"

# Test authentication endpoints
echo "🔐 Testing Authentication Endpoints"
echo "===================================="

# Test admin login
echo "👨‍💼 Testing Admin Login..."
ADMIN_RESPONSE=$(curl -s -X POST http://localhost:3002/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"superadmin123"}')

echo "Admin Login Response: $ADMIN_RESPONSE"

# Extract token from admin response (if successful)
ADMIN_TOKEN=$(echo $ADMIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$ADMIN_TOKEN" ]; then
    echo "✅ Admin login successful! Token: ${ADMIN_TOKEN:0:20}..."
else
    echo "❌ Admin login failed!"
fi

echo -e "\n"

# Test seller/farmer login
echo "👨‍🌾 Testing Farmer/Seller Login..."
SELLER_RESPONSE=$(curl -s -X POST http://localhost:3001/api/v1/sellers/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"farmer123"}')

echo "Seller Login Response: $SELLER_RESPONSE"

# Extract token from seller response (if successful)
SELLER_TOKEN=$(echo $SELLER_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$SELLER_TOKEN" ]; then
    echo "✅ Seller login successful! Token: ${SELLER_TOKEN:0:20}..."
else
    echo "❌ Seller login failed!"
fi

echo -e "\n"



# Display login credentials
echo "📋 Available Login Credentials"
echo "=============================="
echo "Admin Accounts:"
echo "  <EMAIL> / superadmin123 (SUPER_ADMIN)"
echo "  <EMAIL> / admin123 (ADMIN)"
echo "  <EMAIL> / support123 (SUPPORT)"
echo ""
echo "Farmer/Seller Accounts (all use password: farmer123):"
echo "  <EMAIL> (Andhra Pradesh)"
echo "  <EMAIL> (Gujarat)"
echo "  <EMAIL> (Punjab)"
echo "  <EMAIL> (Rajasthan)"
echo "  <EMAIL> (Uttar Pradesh)"
echo "  <EMAIL> (Bihar)"
echo ""
echo "📖 For detailed credentials, see LOGIN_CREDENTIALS.md"

echo -e "\n"
echo "✅ Seeding and authentication test completed!"
echo "🔗 Services should be running on:"
echo "   - Seller Service: http://localhost:3001"
echo "   - Admin Service: http://localhost:3002"
echo "   - Analytics Service: http://localhost:3003"
echo "   - Crop Service: http://localhost:3004"
echo "   - Farm Service: http://localhost:3005"
echo "   - Notification Service: http://localhost:3008"
echo "   - Order Service: http://localhost:3009"

echo -e "\n\n"

# Test seed farmers and farms only
echo "Testing seed farmers and farms only..."
curl -X POST http://localhost:3333/api/seed \
  -H "Content-Type: application/json" \
  -d "{\"apiKey\": \"$API_KEY\", \"seedTypes\": [\"farmers\", \"farms\"]}"

echo -e "\n\n"

# Test health endpoint
echo "Testing health endpoint..."
curl http://localhost:3333/api/health 