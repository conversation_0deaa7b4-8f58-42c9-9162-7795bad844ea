{"info": {"name": "AgriTech Crop Service", "description": "Crop management, marketplace, and enhanced search", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3004/api/crops", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "crop_id", "value": "", "type": "string"}, {"key": "seller_id", "value": "seller_123", "type": "string"}, {"key": "farm_id", "value": "farm_456", "type": "string"}, {"key": "plot_id", "value": "plot_789", "type": "string"}], "item": [{"name": "Service Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3004/api/health", "protocol": "http", "host": ["localhost"], "port": "3004", "path": ["api", "health"]}}, "response": []}, {"name": "Crop Service Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "Test Search Functionality", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/test-search?q=tomato", "host": ["{{base_url}}"], "path": ["test-search"], "query": [{"key": "q", "value": "tomato"}]}}, "response": []}, {"name": "Crop CRUD Operations", "item": [{"name": "Get All Crops", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}?page=1&limit=10", "host": ["{{base_url}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category", "value": "VEGETABLES", "disabled": true}, {"key": "organic", "value": "true", "disabled": true}, {"key": "available", "value": "true", "disabled": true}]}}, "response": []}, {"name": "Get Crop by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{crop_id}}", "host": ["{{base_url}}"], "path": ["{{crop_id}}"]}}, "response": []}, {"name": "C<PERSON> <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.crop && response.crop.id) {", "        pm.environment.set('crop_id', response.crop.id);", "        pm.collectionVariables.set('crop_id', response.crop.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"plotId\": \"{{plot_id}}\",\n  \"farmId\": \"{{farm_id}}\",\n  \"sellerId\": \"{{seller_id}}\",\n  \"name\": \"Organic Cherry Tomatoes\",\n  \"type\": \"VEGETABLE\",\n  \"variety\": \"Cherry\",\n  \"plantingDate\": \"2024-01-15T00:00:00.000Z\",\n  \"expectedHarvestDate\": \"2024-04-15T00:00:00.000Z\",\n  \"soilConditions\": {\n    \"type\": \"LOAMY\",\n    \"ph\": 6.5,\n    \"nutrients\": [\"NITROGEN\", \"PHOSPHORUS\", \"POTASSIUM\"]\n  },\n  \"waterAvailability\": \"ADEQUATE\",\n  \"metadata\": {\n    \"cropCategory\": \"VEGETABLES\",\n    \"farmingMethod\": \"ORGANIC\",\n    \"irrigationMethod\": \"DRIP\",\n    \"harvestSeason\": \"SUMMER\",\n    \"waterSource\": \"BOREWELL\",\n    \"seedType\": \"HYBRID\"\n  },\n  \"cultivation\": {\n    \"irrigationNeeds\": \"Regular watering every 2-3 days\",\n    \"fertilizerRequirements\": \"Organic compost and vermicompost\",\n    \"pestControl\": \"Neem oil and organic pesticides\",\n    \"climateConditions\": \"Warm and humid climate preferred\"\n  },\n  \"yield\": {\n    \"expected\": 5000,\n    \"actual\": 0,\n    \"unit\": \"KG\"\n  },\n  \"resources\": {\n    \"water\": 1000,\n    \"fertilizer\": 50,\n    \"pesticides\": 10\n  },\n  \"health\": {\n    \"status\": \"HEALTHY\",\n    \"issues\": [],\n    \"lastCheck\": \"2024-01-15T00:00:00.000Z\"\n  },\n  \"images\": [\"https://example.com/tomato1.jpg\"],\n  \"tags\": [\"organic\", \"cherry\", \"tomato\", \"fresh\"],\n  \"certifications\": [\"ORGANIC\", \"PESTICIDE_FREE\"]\n}"}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": []}, {"name": "Update Crop", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Organic Cherry Tomatoes\",\n  \"actualHarvestDate\": \"2024-04-12T00:00:00.000Z\",\n  \"growthStage\": \"HARVESTED\",\n  \"yield\": {\n    \"actual\": 4500,\n    \"unit\": \"KG\"\n  },\n  \"health\": {\n    \"status\": \"HEALTHY\",\n    \"issues\": [],\n    \"lastCheck\": \"2024-04-12T00:00:00.000Z\"\n  },\n  \"resources\": {\n    \"water\": 1200,\n    \"fertilizer\": 60,\n    \"pesticides\": 15\n  },\n  \"tags\": [\"organic\", \"cherry\", \"tomato\", \"fresh\", \"harvested\"]\n}"}, "url": {"raw": "{{base_url}}/{{crop_id}}", "host": ["{{base_url}}"], "path": ["{{crop_id}}"]}}, "response": []}, {"name": "Delete Crop", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/{{crop_id}}", "host": ["{{base_url}}"], "path": ["{{crop_id}}"]}}, "response": []}]}, {"name": "Crop Lifecycle Management", "item": [{"name": "Update Growth Stage", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"growthStage\": \"FLOWERING\",\n  \"notes\": \"Crop has entered flowering stage, looking healthy\"\n}"}, "url": {"raw": "{{base_url}}/{{crop_id}}/growth-stage", "host": ["{{base_url}}"], "path": ["{{crop_id}}", "growth-stage"]}}, "response": []}, {"name": "Update Health Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"healthStatus\": \"HEALTHY\",\n  \"issues\": [],\n  \"notes\": \"Regular health check completed, no issues found\"\n}"}, "url": {"raw": "{{base_url}}/{{crop_id}}/health", "host": ["{{base_url}}"], "path": ["{{crop_id}}", "health"]}}, "response": []}, {"name": "Record Harvest", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"actualYield\": 4500,\n  \"harvestDate\": \"2024-04-15T00:00:00.000Z\",\n  \"quality\": \"PREMIUM\"\n}"}, "url": {"raw": "{{base_url}}/{{crop_id}}/harvest", "host": ["{{base_url}}"], "path": ["{{crop_id}}", "harvest"]}}, "response": []}, {"name": "Add Maintenance Activity", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"IRRIGATION\",\n  \"date\": \"2024-03-15T00:00:00.000Z\",\n  \"description\": \"Regular irrigation performed\",\n  \"performedBy\": \"Farm Worker\",\n  \"cost\": 50,\n  \"resources\": [\"water\"]\n}"}, "url": {"raw": "{{base_url}}/{{crop_id}}/maintenance", "host": ["{{base_url}}"], "path": ["{{crop_id}}", "maintenance"]}}, "response": []}, {"name": "Get Crop Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/{{seller_id}}?farmId={{farm_id}}", "host": ["{{base_url}}"], "path": ["analytics", "{{seller_id}}"], "query": [{"key": "farmId", "value": "{{farm_id}}", "disabled": true}, {"key": "fromDate", "value": "2024-01-01", "disabled": true}, {"key": "toDate", "value": "2024-12-31", "disabled": true}]}}, "response": []}, {"name": "Reindex All Crops", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/reindex", "host": ["{{base_url}}"], "path": ["reindex"]}}, "response": []}]}, {"name": "Crop Search & Discovery", "item": [{"name": "Search Crops", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/search?query=tomato&sellerId={{seller_id}}&farmId={{farm_id}}&growthStage=GROWING&page=1&limit=10", "host": ["{{base_url}}"], "path": ["search"], "query": [{"key": "query", "value": "tomato"}, {"key": "sellerId", "value": "{{seller_id}}", "disabled": true}, {"key": "farmId", "value": "{{farm_id}}", "disabled": true}, {"key": "plotId", "value": "{{plot_id}}", "disabled": true}, {"key": "growthStage", "value": "GROWING", "disabled": true}, {"key": "healthStatus", "value": "HEALTHY", "disabled": true}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "harvestDateFrom", "value": "2024-01-01", "disabled": true}, {"key": "harvestDateTo", "value": "2024-12-31", "disabled": true}]}}, "response": []}, {"name": "Get Crop Suggestions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/suggestions?q=tom&limit=5", "host": ["{{base_url}}"], "path": ["suggestions"], "query": [{"key": "q", "value": "tom"}, {"key": "limit", "value": "5"}]}}, "response": []}, {"name": "Get Popular Crops", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/popular?limit=10&category=VEGETABLES", "host": ["{{base_url}}"], "path": ["popular"], "query": [{"key": "limit", "value": "10"}, {"key": "category", "value": "VEGETABLES"}]}}, "response": []}, {"name": "Get Nearby Crops", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/nearby?latitude=12.9716&longitude=77.5946&radius=25&available=true", "host": ["{{base_url}}"], "path": ["nearby"], "query": [{"key": "latitude", "value": "12.9716"}, {"key": "longitude", "value": "77.5946"}, {"key": "radius", "value": "25"}, {"key": "available", "value": "true"}]}}, "response": []}]}, {"name": "Crop Marketplace", "item": [{"name": "Get Marketplace Listings", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/marketplace?category=VEGETABLES&available=true&page=1&limit=20", "host": ["{{base_url}}"], "path": ["marketplace"], "query": [{"key": "category", "value": "VEGETABLES"}, {"key": "available", "value": "true"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "sortBy", "value": "price", "disabled": true}, {"key": "sortOrder", "value": "asc", "disabled": true}]}}, "response": []}, {"name": "Create Marketplace Listing", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cropId\": \"{{crop_id}}\",\n  \"title\": \"Fresh Organic Cherry Tomatoes - Premium Quality\",\n  \"description\": \"Freshly harvested organic cherry tomatoes from our certified organic farm. Perfect for salads and cooking.\",\n  \"pricing\": {\n    \"pricePerKg\": 75.00,\n    \"minimumOrder\": 10,\n    \"bulkDiscounts\": [\n      {\n        \"quantity\": 50,\n        \"discountPercent\": 5\n      },\n      {\n        \"quantity\": 100,\n        \"discountPercent\": 10\n      }\n    ]\n  },\n  \"availability\": {\n    \"quantityAvailable\": 4500,\n    \"availableUntil\": \"2024-05-15\"\n  },\n  \"delivery\": {\n    \"methods\": [\"PICKUP\", \"LOCAL_DELIVERY\"],\n    \"deliveryRadius\": 50,\n    \"deliveryCharge\": 25.00\n  },\n  \"images\": [\n    \"https://example.com/tomato1.jpg\",\n    \"https://example.com/tomato2.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/marketplace", "host": ["{{base_url}}"], "path": ["marketplace"]}}, "response": []}, {"name": "Update Marketplace Listing", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"pricing\": {\n    \"pricePerKg\": 70.00\n  },\n  \"availability\": {\n    \"quantityAvailable\": 4000\n  }\n}"}, "url": {"raw": "{{base_url}}/marketplace/{{listing_id}}", "host": ["{{base_url}}"], "path": ["marketplace", "{{listing_id}}"]}}, "response": []}]}, {"name": "Crop Analytics & Insights", "item": [{"name": "Get Crop Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{crop_id}}/analytics", "host": ["{{base_url}}"], "path": ["{{crop_id}}", "analytics"]}}, "response": []}, {"name": "Get Market Insights", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/insights/market?category=VEGETABLES&region=south&period=monthly", "host": ["{{base_url}}"], "path": ["insights", "market"], "query": [{"key": "category", "value": "VEGETABLES"}, {"key": "region", "value": "south"}, {"key": "period", "value": "monthly"}]}}, "response": []}, {"name": "Get Price Trends", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/insights/price-trends?crop=tomato&period=6months&region=karnataka", "host": ["{{base_url}}"], "path": ["insights", "price-trends"], "query": [{"key": "crop", "value": "tomato"}, {"key": "period", "value": "6months"}, {"key": "region", "value": "karnataka"}]}}, "response": []}, {"name": "Get Crop Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/statistics?groupBy=category&organic=true&period=yearly", "host": ["{{base_url}}"], "path": ["statistics"], "query": [{"key": "groupBy", "value": "category"}, {"key": "organic", "value": "true"}, {"key": "period", "value": "yearly"}]}}, "response": []}]}, {"name": "Crop Quality & Certification", "item": [{"name": "Submit Quality Report", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cropId\": \"{{crop_id}}\",\n  \"qualityTests\": [\n    {\n      \"testType\": \"PESTICIDE_RESIDUE\",\n      \"result\": \"PASS\",\n      \"details\": \"No pesticide residues detected\",\n      \"testedBy\": \"AgriLab Certified\",\n      \"testDate\": \"2024-04-10\"\n    },\n    {\n      \"testType\": \"NUTRITIONAL_ANALYSIS\",\n      \"result\": \"PASS\",\n      \"details\": \"High vitamin C content, good mineral profile\",\n      \"testedBy\": \"Food Testing Lab\",\n      \"testDate\": \"2024-04-10\"\n    }\n  ],\n  \"overallGrade\": \"A+\",\n  \"certifications\": [\"ORGANIC\", \"PESTICIDE_FREE\", \"PREMIUM_QUALITY\"]\n}"}, "url": {"raw": "{{base_url}}/{{crop_id}}/quality", "host": ["{{base_url}}"], "path": ["{{crop_id}}", "quality"]}}, "response": []}, {"name": "Get Quality Report", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{crop_id}}/quality", "host": ["{{base_url}}"], "path": ["{{crop_id}}", "quality"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set crop-specific timestamp", "pm.globals.set('crop_timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global crop service tests", "pm.test('Crop API response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// Test for crop-specific responses", "if (pm.response.code >= 200 && pm.response.code < 300) {", "    pm.test('Response contains valid crop data', function () {", "        const response = pm.response.json();", "        pm.expect(response).to.be.an('object');", "        if (response.crop) {", "            pm.expect(response.crop).to.have.property('name');", "        }", "    });", "}"]}}]}