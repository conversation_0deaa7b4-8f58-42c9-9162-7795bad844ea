{"info": {"name": "AgriTech Analytics Service", "description": "Analytics, reporting, and business intelligence", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3003/api/analytics", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "report_id", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3003/health", "protocol": "http", "host": ["localhost"], "port": "3003", "path": ["health"]}}, "response": []}, {"name": "Dashboard Analytics", "item": [{"name": "Get Dashboard Overview", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/dashboard", "host": ["{{base_url}}"], "path": ["dashboard"]}}, "response": []}, {"name": "Get Dashboard Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/dashboard/metrics?period=monthly&year=2024", "host": ["{{base_url}}"], "path": ["dashboard", "metrics"], "query": [{"key": "period", "value": "monthly"}, {"key": "year", "value": "2024"}]}}, "response": []}, {"name": "Get Real-time Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/dashboard/realtime", "host": ["{{base_url}}"], "path": ["dashboard", "realtime"]}}, "response": []}]}, {"name": "Sales Analytics", "item": [{"name": "Get Sales Overview", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sales?period=monthly&startDate=2024-01-01&endDate=2024-12-31", "host": ["{{base_url}}"], "path": ["sales"], "query": [{"key": "period", "value": "monthly"}, {"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}]}}, "response": []}, {"name": "Get Sales by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sales/category?period=quarterly&year=2024", "host": ["{{base_url}}"], "path": ["sales", "category"], "query": [{"key": "period", "value": "quarterly"}, {"key": "year", "value": "2024"}]}}, "response": []}, {"name": "Get Sales by Region", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sales/region?period=monthly&region=south", "host": ["{{base_url}}"], "path": ["sales", "region"], "query": [{"key": "period", "value": "monthly"}, {"key": "region", "value": "south"}]}}, "response": []}, {"name": "Get Top Selling Crops", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sales/top-crops?limit=10&period=monthly", "host": ["{{base_url}}"], "path": ["sales", "top-crops"], "query": [{"key": "limit", "value": "10"}, {"key": "period", "value": "monthly"}]}}, "response": []}]}, {"name": "Crop Analytics", "item": [{"name": "Get Crop Performance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/crops?category=VEGETABLES&period=monthly&organic=true", "host": ["{{base_url}}"], "path": ["crops"], "query": [{"key": "category", "value": "VEGETABLES"}, {"key": "period", "value": "monthly"}, {"key": "organic", "value": "true"}]}}, "response": []}, {"name": "Get Crop Yield Analysis", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/crops/yield?crop=tomato&region=karnataka&period=yearly", "host": ["{{base_url}}"], "path": ["crops", "yield"], "query": [{"key": "crop", "value": "tomato"}, {"key": "region", "value": "karnataka"}, {"key": "period", "value": "yearly"}]}}, "response": []}, {"name": "Get Seasonal Trends", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/crops/seasonal?year=2024&category=VEGETABLES", "host": ["{{base_url}}"], "path": ["crops", "seasonal"], "query": [{"key": "year", "value": "2024"}, {"key": "category", "value": "VEGETABLES"}]}}, "response": []}]}, {"name": "User Analytics", "item": [{"name": "Get User Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users?period=monthly&userType=seller", "host": ["{{base_url}}"], "path": ["users"], "query": [{"key": "period", "value": "monthly"}, {"key": "userType", "value": "seller"}]}}, "response": []}, {"name": "Get User Engagement", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/engagement?period=weekly&metric=active_users", "host": ["{{base_url}}"], "path": ["users", "engagement"], "query": [{"key": "period", "value": "weekly"}, {"key": "metric", "value": "active_users"}]}}, "response": []}, {"name": "Get User Growth", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/growth?period=monthly&year=2024", "host": ["{{base_url}}"], "path": ["users", "growth"], "query": [{"key": "period", "value": "monthly"}, {"key": "year", "value": "2024"}]}}, "response": []}]}, {"name": "Report Generation", "item": [{"name": "Generate Custom Report", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 202) {", "    const response = pm.response.json();", "    if (response.reportId) {", "        pm.environment.set('report_id', response.reportId);", "        pm.collectionVariables.set('report_id', response.reportId);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reportType\": \"SALES_SUMMARY\",\n  \"parameters\": {\n    \"startDate\": \"2024-01-01\",\n    \"endDate\": \"2024-12-31\",\n    \"groupBy\": \"month\",\n    \"includeCharts\": true,\n    \"filters\": {\n      \"category\": \"VEGETABLES\",\n      \"organic\": true,\n      \"region\": \"south\"\n    }\n  },\n  \"format\": \"PDF\",\n  \"deliveryMethod\": \"EMAIL\",\n  \"recipients\": [\"<EMAIL>\"]\n}"}, "url": {"raw": "{{base_url}}/reports/generate", "host": ["{{base_url}}"], "path": ["reports", "generate"]}}, "response": []}, {"name": "Get Report Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reports/{{report_id}}", "host": ["{{base_url}}"], "path": ["reports", "{{report_id}}"]}}, "response": []}, {"name": "Download Report", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reports/{{report_id}}/download", "host": ["{{base_url}}"], "path": ["reports", "{{report_id}}", "download"]}}, "response": []}, {"name": "Get Report Templates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/reports/templates", "host": ["{{base_url}}"], "path": ["reports", "templates"]}}, "response": []}]}, {"name": "Market Intelligence", "item": [{"name": "Get Market Insights", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/market/insights?region=south&category=VEGETABLES&period=monthly", "host": ["{{base_url}}"], "path": ["market", "insights"], "query": [{"key": "region", "value": "south"}, {"key": "category", "value": "VEGETABLES"}, {"key": "period", "value": "monthly"}]}}, "response": []}, {"name": "Get Price Predictions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/market/predictions?crop=tomato&region=karnataka&horizon=3months", "host": ["{{base_url}}"], "path": ["market", "predictions"], "query": [{"key": "crop", "value": "tomato"}, {"key": "region", "value": "karnataka"}, {"key": "horizon", "value": "3months"}]}}, "response": []}, {"name": "Get Demand Forecast", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/market/demand?category=VEGETABLES&region=south&period=quarterly", "host": ["{{base_url}}"], "path": ["market", "demand"], "query": [{"key": "category", "value": "VEGETABLES"}, {"key": "region", "value": "south"}, {"key": "period", "value": "quarterly"}]}}, "response": []}]}, {"name": "Data Export", "item": [{"name": "Export Analytics Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"dataType\": \"SALES_DATA\",\n  \"format\": \"CSV\",\n  \"dateRange\": {\n    \"startDate\": \"2024-01-01\",\n    \"endDate\": \"2024-12-31\"\n  },\n  \"filters\": {\n    \"category\": \"VEGETABLES\",\n    \"region\": \"south\",\n    \"organic\": true\n  },\n  \"fields\": [\n    \"date\",\n    \"crop_name\",\n    \"quantity_sold\",\n    \"revenue\",\n    \"seller_name\",\n    \"region\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/export", "host": ["{{base_url}}"], "path": ["export"]}}, "response": []}, {"name": "Get Export Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/export/{{export_id}}", "host": ["{{base_url}}"], "path": ["export", "{{export_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set analytics timestamp", "pm.globals.set('analytics_timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global analytics service tests", "pm.test('Analytics API response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Test for analytics data structure", "if (pm.response.code >= 200 && pm.response.code < 300) {", "    pm.test('Response contains analytics data', function () {", "        const response = pm.response.json();", "        pm.expect(response).to.be.an('object');", "    });", "}"]}}]}