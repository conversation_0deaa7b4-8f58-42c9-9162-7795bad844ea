{"info": {"name": "AgriTech Notification Service", "description": "Notification management and communication", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3008/api/notifications", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "notification_id", "value": "", "type": "string"}, {"key": "template_id", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3008/health", "protocol": "http", "host": ["localhost"], "port": "3008", "path": ["health"]}}, "response": []}, {"name": "Notification Management", "item": [{"name": "Get User Notifications", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}?page=1&limit=20&unread=true", "host": ["{{base_url}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "unread", "value": "true"}, {"key": "type", "value": "order", "disabled": true}, {"key": "priority", "value": "high", "disabled": true}]}}, "response": []}, {"name": "Send Notification", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.notification && response.notification.id) {", "        pm.environment.set('notification_id', response.notification.id);", "        pm.collectionVariables.set('notification_id', response.notification.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{seller_id}}\",\n  \"title\": \"New Order Received\",\n  \"message\": \"You have received a new order for your organic tomatoes. Please review and confirm.\",\n  \"type\": \"ORDER\",\n  \"priority\": \"HIGH\",\n  \"channels\": [\"PUSH\", \"EMAIL\"],\n  \"data\": {\n    \"orderId\": \"order-123\",\n    \"cropName\": \"Organic Tomatoes\",\n    \"quantity\": 50,\n    \"amount\": 3750.00\n  },\n  \"actionButtons\": [\n    {\n      \"label\": \"View Order\",\n      \"action\": \"VIEW_ORDER\",\n      \"url\": \"/orders/order-123\"\n    },\n    {\n      \"label\": \"Accept\",\n      \"action\": \"ACCEPT_ORDER\",\n      \"url\": \"/orders/order-123/accept\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": []}, {"name": "Mark Notification as Read", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/{{notification_id}}/read", "host": ["{{base_url}}"], "path": ["{{notification_id}}", "read"]}}, "response": []}, {"name": "<PERSON> as <PERSON>", "request": {"method": "PUT", "header": [], "url": {"raw": "{{base_url}}/read-all", "host": ["{{base_url}}"], "path": ["read-all"]}}, "response": []}, {"name": "Delete Notification", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/{{notification_id}}", "host": ["{{base_url}}"], "path": ["{{notification_id}}"]}}, "response": []}]}, {"name": "Bulk Notifications", "item": [{"name": "Send Bulk Notifications", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipients\": [\n    \"{{seller_id}}\",\n    \"seller-456\",\n    \"seller-789\"\n  ],\n  \"title\": \"Market Price Update\",\n  \"message\": \"Tomato prices have increased by 15% in your region. Consider updating your crop prices.\",\n  \"type\": \"MARKET_UPDATE\",\n  \"priority\": \"MEDIUM\",\n  \"channels\": [\"PUSH\", \"SMS\"],\n  \"data\": {\n    \"crop\": \"tomato\",\n    \"priceChange\": 15,\n    \"region\": \"Karnataka\"\n  },\n  \"scheduledFor\": null\n}"}, "url": {"raw": "{{base_url}}/bulk", "host": ["{{base_url}}"], "path": ["bulk"]}}, "response": []}, {"name": "Send Targeted Notifications", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"criteria\": {\n    \"userType\": \"SELLER\",\n    \"location\": {\n      \"state\": \"Karnataka\",\n      \"district\": \"Bangalore\"\n    },\n    \"cropCategories\": [\"VEGETABLES\"],\n    \"verified\": true\n  },\n  \"title\": \"Weather Alert\",\n  \"message\": \"Heavy rainfall expected in your area. Please take necessary precautions for your crops.\",\n  \"type\": \"WEATHER_ALERT\",\n  \"priority\": \"HIGH\",\n  \"channels\": [\"PUSH\", \"SMS\", \"EMAIL\"],\n  \"data\": {\n    \"alertType\": \"HEAVY_RAIN\",\n    \"severity\": \"HIGH\",\n    \"validUntil\": \"2024-04-20T18:00:00Z\"\n  }\n}"}, "url": {"raw": "{{base_url}}/targeted", "host": ["{{base_url}}"], "path": ["targeted"]}}, "response": []}]}, {"name": "Notification Templates", "item": [{"name": "Get All Templates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/templates?category=ORDER&active=true", "host": ["{{base_url}}"], "path": ["templates"], "query": [{"key": "category", "value": "ORDER"}, {"key": "active", "value": "true"}]}}, "response": []}, {"name": "Create Template", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.template && response.template.id) {", "        pm.environment.set('template_id', response.template.id);", "        pm.collectionVariables.set('template_id', response.template.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Order Confirmation\",\n  \"category\": \"ORDER\",\n  \"title\": \"Order Confirmed - {{orderNumber}}\",\n  \"message\": \"Your order for {{cropName}} ({{quantity}} kg) has been confirmed. Expected delivery: {{deliveryDate}}\",\n  \"type\": \"ORDER_CONFIRMATION\",\n  \"channels\": [\"PUSH\", \"EMAIL\"],\n  \"variables\": [\n    {\n      \"name\": \"orderNumber\",\n      \"type\": \"string\",\n      \"required\": true\n    },\n    {\n      \"name\": \"cropName\",\n      \"type\": \"string\",\n      \"required\": true\n    },\n    {\n      \"name\": \"quantity\",\n      \"type\": \"number\",\n      \"required\": true\n    },\n    {\n      \"name\": \"deliveryDate\",\n      \"type\": \"date\",\n      \"required\": true\n    }\n  ],\n  \"actionButtons\": [\n    {\n      \"label\": \"Track Order\",\n      \"action\": \"TRACK_ORDER\",\n      \"url\": \"/orders/{{orderNumber}}/track\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/templates", "host": ["{{base_url}}"], "path": ["templates"]}}, "response": []}, {"name": "Update Template", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Order Confirmed - {{orderNumber}} (Updated)\",\n  \"message\": \"Your order for {{cropName}} ({{quantity}} kg) has been confirmed and is being processed. Expected delivery: {{deliveryDate}}\",\n  \"active\": true\n}"}, "url": {"raw": "{{base_url}}/templates/{{template_id}}", "host": ["{{base_url}}"], "path": ["templates", "{{template_id}}"]}}, "response": []}, {"name": "Send Template Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"templateId\": \"{{template_id}}\",\n  \"to\": \"{{seller_id}}\",\n  \"variables\": {\n    \"orderNumber\": \"ORD-2024-001\",\n    \"cropName\": \"Organic Tomatoes\",\n    \"quantity\": 50,\n    \"deliveryDate\": \"2024-04-25\"\n  },\n  \"priority\": \"MEDIUM\"\n}"}, "url": {"raw": "{{base_url}}/templates/send", "host": ["{{base_url}}"], "path": ["templates", "send"]}}, "response": []}]}, {"name": "User Preferences", "item": [{"name": "Get User Preferences", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/preferences", "host": ["{{base_url}}"], "path": ["preferences"]}}, "response": []}, {"name": "Update User Preferences", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"channels\": {\n    \"email\": {\n      \"enabled\": true,\n      \"types\": [\"ORDER\", \"MARKET_UPDATE\", \"WEATHER_ALERT\"]\n    },\n    \"sms\": {\n      \"enabled\": true,\n      \"types\": [\"ORDER\", \"WEATHER_ALERT\"]\n    },\n    \"push\": {\n      \"enabled\": true,\n      \"types\": [\"ORDER\", \"MARKET_UPDATE\", \"WEATHER_ALERT\", \"SYSTEM\"]\n    }\n  },\n  \"frequency\": {\n    \"marketUpdates\": \"DAILY\",\n    \"weatherAlerts\": \"IMMEDIATE\",\n    \"systemNotifications\": \"WEEKLY\"\n  },\n  \"quietHours\": {\n    \"enabled\": true,\n    \"start\": \"22:00\",\n    \"end\": \"07:00\",\n    \"timezone\": \"Asia/Kolkata\"\n  }\n}"}, "url": {"raw": "{{base_url}}/preferences", "host": ["{{base_url}}"], "path": ["preferences"]}}, "response": []}]}, {"name": "Notification Analytics", "item": [{"name": "Get Notification Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/stats?period=monthly&type=ORDER", "host": ["{{base_url}}"], "path": ["analytics", "stats"], "query": [{"key": "period", "value": "monthly"}, {"key": "type", "value": "ORDER"}]}}, "response": []}, {"name": "Get Delivery Reports", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/delivery?startDate=2024-01-01&endDate=2024-12-31&channel=EMAIL", "host": ["{{base_url}}"], "path": ["analytics", "delivery"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}, {"key": "channel", "value": "EMAIL"}]}}, "response": []}, {"name": "Get Engagement Metrics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/engagement?period=weekly&metric=open_rate", "host": ["{{base_url}}"], "path": ["analytics", "engagement"], "query": [{"key": "period", "value": "weekly"}, {"key": "metric", "value": "open_rate"}]}}, "response": []}]}, {"name": "Scheduled Notifications", "item": [{"name": "Schedule Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to\": \"{{seller_id}}\",\n  \"title\": \"Harvest Reminder\",\n  \"message\": \"Your tomato crop is ready for harvest. Don't forget to update your availability status.\",\n  \"type\": \"HARVEST_REMINDER\",\n  \"priority\": \"MEDIUM\",\n  \"channels\": [\"PUSH\", \"EMAIL\"],\n  \"scheduledFor\": \"2024-04-15T08:00:00Z\",\n  \"recurring\": {\n    \"enabled\": false,\n    \"frequency\": \"WEEKLY\",\n    \"endDate\": null\n  }\n}"}, "url": {"raw": "{{base_url}}/schedule", "host": ["{{base_url}}"], "path": ["schedule"]}}, "response": []}, {"name": "Get Scheduled Notifications", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/schedule?status=PENDING&page=1&limit=10", "host": ["{{base_url}}"], "path": ["schedule"], "query": [{"key": "status", "value": "PENDING"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}, "response": []}, {"name": "Cancel Scheduled Notification", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/schedule/{{schedule_id}}", "host": ["{{base_url}}"], "path": ["schedule", "{{schedule_id}}"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set notification timestamp", "pm.globals.set('notification_timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global notification service tests", "pm.test('Notification API response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// Test for notification responses", "if (pm.response.code >= 200 && pm.response.code < 300) {", "    pm.test('Response has valid notification structure', function () {", "        const response = pm.response.json();", "        pm.expect(response).to.be.an('object');", "    });", "}"]}}]}