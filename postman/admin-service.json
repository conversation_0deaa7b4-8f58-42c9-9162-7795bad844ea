{"info": {"name": "AgriTech Admin Service", "description": "Administrative functions and system management", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3002/api/v1/admin", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "admin_id", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3002/health", "protocol": "http", "host": ["localhost"], "port": "3002", "path": ["health"]}}, "response": []}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('admin_token', response.token);", "        pm.collectionVariables.set('admin_token', response.token);", "    }", "    if (response.admin && response.admin.id) {", "        pm.environment.set('admin_id', response.admin.id);", "        pm.collectionVariables.set('admin_id', response.admin.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"superadmin123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}}, "response": []}]}, {"name": "Dashboard & Analytics", "item": [{"name": "Admin Dashboard", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/dashboard", "host": ["{{base_url}}"], "path": ["dashboard"]}}, "response": []}, {"name": "System Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/health", "host": ["{{base_url}}"], "path": ["system", "health"]}}, "response": []}, {"name": "System Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/system/stats", "host": ["{{base_url}}"], "path": ["system", "stats"]}}, "response": []}]}, {"name": "Seller Management", "item": [{"name": "Get All Sellers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sellers?page=1&limit=10", "host": ["{{base_url}}"], "path": ["sellers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "verified", "value": "false", "disabled": true}, {"key": "state", "value": "Karnataka", "disabled": true}]}}, "response": []}, {"name": "Get Seller by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/sellers/{{seller_id}}", "host": ["{{base_url}}"], "path": ["sellers", "{{seller_id}}"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"verified\": true,\n  \"verificationNotes\": \"All documents verified and approved\",\n  \"verifiedBy\": \"{{admin_id}}\"\n}"}, "url": {"raw": "{{base_url}}/sellers/{{seller_id}}/verify", "host": ["{{base_url}}"], "path": ["sellers", "{{seller_id}}", "verify"]}}, "response": []}, {"name": "Update Seller", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\",\n  \"notes\": \"Updated by admin\"\n}"}, "url": {"raw": "{{base_url}}/sellers/{{seller_id}}", "host": ["{{base_url}}"], "path": ["sellers", "{{seller_id}}"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"SUSPENDED\",\n  \"reason\": \"Policy violation\",\n  \"suspendedBy\": \"{{admin_id}}\"\n}"}, "url": {"raw": "{{base_url}}/sellers/{{seller_id}}/suspend", "host": ["{{base_url}}"], "path": ["sellers", "{{seller_id}}", "suspend"]}}, "response": []}]}, {"name": "Farm Management", "item": [{"name": "Get All Farms", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/farms?page=1&limit=10", "host": ["{{base_url}}"], "path": ["farms"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "verified", "value": "false", "disabled": true}]}}, "response": []}, {"name": "Get Farm by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/farms/{{farm_id}}", "host": ["{{base_url}}"], "path": ["farms", "{{farm_id}}"]}}, "response": []}, {"name": "Verify Farm", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"verified\": true,\n  \"verificationNotes\": \"Farm location and details verified\",\n  \"verifiedBy\": \"{{admin_id}}\"\n}"}, "url": {"raw": "{{base_url}}/farms/{{farm_id}}/verify", "host": ["{{base_url}}"], "path": ["farms", "{{farm_id}}", "verify"]}}, "response": []}]}, {"name": "Crop Management", "item": [{"name": "Get All Crops", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/crops?page=1&limit=10", "host": ["{{base_url}}"], "path": ["crops"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "PENDING_APPROVAL", "disabled": true}]}}, "response": []}, {"name": "Approve Crop", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"APPROVED\",\n  \"approvalNotes\": \"Crop details verified and approved\",\n  \"approvedBy\": \"{{admin_id}}\"\n}"}, "url": {"raw": "{{base_url}}/crops/{{crop_id}}/approve", "host": ["{{base_url}}"], "path": ["crops", "{{crop_id}}", "approve"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set admin timestamp", "pm.globals.set('admin_timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global admin test for response time", "pm.test('Admin API response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// Check for admin authorization", "if (pm.response.code === 401) {", "    pm.test('Unauthorized - Admin token may be expired', function () {", "        pm.expect.fail('Admin authentication required');", "    });", "}"]}}]}