{"info": {"name": "AgriTech Order Service", "description": "Order processing and lifecycle management", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3009/api/orders", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "order_id", "value": "", "type": "string"}, {"key": "payment_id", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3009/health", "protocol": "http", "host": ["localhost"], "port": "3009", "path": ["health"]}}, "response": []}, {"name": "Order Management", "item": [{"name": "Get All Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}?page=1&limit=10&status=PENDING", "host": ["{{base_url}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "PENDING"}, {"key": "sellerId", "value": "{{seller_id}}", "disabled": true}, {"key": "buyerId", "value": "{{buyer_id}}", "disabled": true}, {"key": "startDate", "value": "2024-01-01", "disabled": true}, {"key": "endDate", "value": "2024-12-31", "disabled": true}]}}, "response": []}, {"name": "Get Order by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{order_id}}", "host": ["{{base_url}}"], "path": ["{{order_id}}"]}}, "response": []}, {"name": "Create Order", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.order && response.order.id) {", "        pm.environment.set('order_id', response.order.id);", "        pm.collectionVariables.set('order_id', response.order.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"cropId\": \"{{crop_id}}\",\n  \"sellerId\": \"{{seller_id}}\",\n  \"buyerId\": \"buyer-123\",\n  \"quantity\": 50,\n  \"pricePerKg\": 75.00,\n  \"totalAmount\": 3750.00,\n  \"currency\": \"INR\",\n  \"deliveryAddress\": {\n    \"street\": \"123 Market Street\",\n    \"city\": \"Bangalore\",\n    \"state\": \"Karnataka\",\n    \"pincode\": \"560001\",\n    \"country\": \"India\",\n    \"coordinates\": {\n      \"latitude\": 12.9716,\n      \"longitude\": 77.5946\n    }\n  },\n  \"deliveryPreferences\": {\n    \"method\": \"LOCAL_DELIVERY\",\n    \"preferredDate\": \"2024-04-25\",\n    \"timeSlot\": \"MORNING\",\n    \"specialInstructions\": \"Please call before delivery\"\n  },\n  \"paymentMethod\": \"ONLINE\",\n  \"notes\": \"Urgent order for restaurant supply\"\n}"}, "url": {"raw": "{{base_url}}", "host": ["{{base_url}}"]}}, "response": []}, {"name": "Update Order", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"CONFIRMED\",\n  \"estimatedDelivery\": \"2024-04-25T10:00:00Z\",\n  \"notes\": \"Order confirmed by seller. Preparing for delivery.\"\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}", "host": ["{{base_url}}"], "path": ["{{order_id}}"]}}, "response": []}, {"name": "Cancel Order", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Customer requested cancellation\",\n  \"refundAmount\": 3750.00,\n  \"cancelledBy\": \"BUYER\"\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}/cancel", "host": ["{{base_url}}"], "path": ["{{order_id}}", "cancel"]}}, "response": []}]}, {"name": "Order Lifecycle", "item": [{"name": "Accept Order (Seller)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"estimatedDelivery\": \"2024-04-25T10:00:00Z\",\n  \"notes\": \"Order accepted. Fresh harvest available.\"\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}/accept", "host": ["{{base_url}}"], "path": ["{{order_id}}", "accept"]}}, "response": []}, {"name": "Reject Order (Seller)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Insufficient stock available\",\n  \"alternativeOptions\": [\n    {\n      \"cropId\": \"crop-456\",\n      \"quantity\": 30,\n      \"pricePerKg\": 70.00\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}/reject", "host": ["{{base_url}}"], "path": ["{{order_id}}", "reject"]}}, "response": []}, {"name": "<PERSON> as Shipped", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"trackingNumber\": \"TRK123456789\",\n  \"carrier\": \"Local Delivery Service\",\n  \"estimatedDelivery\": \"2024-04-25T14:00:00Z\",\n  \"shippingAddress\": {\n    \"street\": \"123 Market Street\",\n    \"city\": \"Bangalore\",\n    \"state\": \"Karnataka\",\n    \"pincode\": \"560001\"\n  }\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}/ship", "host": ["{{base_url}}"], "path": ["{{order_id}}", "ship"]}}, "response": []}, {"name": "<PERSON> as Delivered", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deliveredAt\": \"2024-04-25T13:30:00Z\",\n  \"receivedBy\": \"<PERSON>\",\n  \"deliveryNotes\": \"Delivered in good condition\",\n  \"signature\": \"base64_signature_data\",\n  \"photos\": [\n    \"https://example.com/delivery-photo1.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}/deliver", "host": ["{{base_url}}"], "path": ["{{order_id}}", "deliver"]}}, "response": []}]}, {"name": "Order Tracking", "item": [{"name": "Get Order Tracking", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{order_id}}/tracking", "host": ["{{base_url}}"], "path": ["{{order_id}}", "tracking"]}}, "response": []}, {"name": "Update Tracking Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"IN_TRANSIT\",\n  \"location\": {\n    \"latitude\": 12.9500,\n    \"longitude\": 77.6000,\n    \"address\": \"Near Silk Board Junction, Bangalore\"\n  },\n  \"timestamp\": \"2024-04-25T11:30:00Z\",\n  \"notes\": \"Package is on the way to delivery address\",\n  \"estimatedArrival\": \"2024-04-25T13:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}/tracking", "host": ["{{base_url}}"], "path": ["{{order_id}}", "tracking"]}}, "response": []}, {"name": "Get Tracking History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{order_id}}/tracking/history", "host": ["{{base_url}}"], "path": ["{{order_id}}", "tracking", "history"]}}, "response": []}]}, {"name": "Payment Processing", "item": [{"name": "Process Payment", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.payment && response.payment.id) {", "        pm.environment.set('payment_id', response.payment.id);", "        pm.collectionVariables.set('payment_id', response.payment.id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"orderId\": \"{{order_id}}\",\n  \"amount\": 3750.00,\n  \"currency\": \"INR\",\n  \"paymentMethod\": \"CARD\",\n  \"paymentDetails\": {\n    \"cardNumber\": \"****************\",\n    \"expiryMonth\": \"12\",\n    \"expiryYear\": \"2025\",\n    \"cvv\": \"123\",\n    \"cardHolderName\": \"John Doe\"\n  },\n  \"billingAddress\": {\n    \"street\": \"123 Payment Street\",\n    \"city\": \"Bangalore\",\n    \"state\": \"Karnataka\",\n    \"pincode\": \"560001\",\n    \"country\": \"India\"\n  }\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}/payment", "host": ["{{base_url}}"], "path": ["{{order_id}}", "payment"]}}, "response": []}, {"name": "Get Payment Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/{{order_id}}/payment", "host": ["{{base_url}}"], "path": ["{{order_id}}", "payment"]}}, "response": []}, {"name": "Process Refund", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentId\": \"{{payment_id}}\",\n  \"refundAmount\": 3750.00,\n  \"reason\": \"Order cancelled by customer\",\n  \"refundMethod\": \"ORIGINAL_PAYMENT_METHOD\"\n}"}, "url": {"raw": "{{base_url}}/{{order_id}}/refund", "host": ["{{base_url}}"], "path": ["{{order_id}}", "refund"]}}, "response": []}]}, {"name": "Order Analytics", "item": [{"name": "Get Order Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics?period=monthly&startDate=2024-01-01&endDate=2024-12-31", "host": ["{{base_url}}"], "path": ["analytics"], "query": [{"key": "period", "value": "monthly"}, {"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}, {"key": "sellerId", "value": "{{seller_id}}", "disabled": true}, {"key": "status", "value": "COMPLETED", "disabled": true}]}}, "response": []}, {"name": "Get Revenue Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/revenue?period=quarterly&year=2024&groupBy=crop", "host": ["{{base_url}}"], "path": ["analytics", "revenue"], "query": [{"key": "period", "value": "quarterly"}, {"key": "year", "value": "2024"}, {"key": "groupBy", "value": "crop"}]}}, "response": []}, {"name": "Get Order Performance", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/analytics/performance?metric=delivery_time&period=monthly", "host": ["{{base_url}}"], "path": ["analytics", "performance"], "query": [{"key": "metric", "value": "delivery_time"}, {"key": "period", "value": "monthly"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set order timestamp", "pm.globals.set('order_timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global order service tests", "pm.test('Order API response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "// Test for order responses", "if (pm.response.code >= 200 && pm.response.code < 300) {", "    pm.test('Response has valid order structure', function () {", "        const response = pm.response.json();", "        pm.expect(response).to.be.an('object');", "    });", "}"]}}]}