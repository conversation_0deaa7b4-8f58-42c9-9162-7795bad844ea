{"id": "agritech-environment", "name": "AgriTech Development Environment", "values": [{"key": "seller_service_url", "value": "http://localhost:3001/api/v1/sellers", "description": "Seller Service direct URL", "enabled": true}, {"key": "admin_service_url", "value": "http://localhost:3002/api/v1/admin", "description": "Admin Service direct URL", "enabled": true}, {"key": "analytics_service_url", "value": "http://localhost:3003/api/analytics", "description": "Analytics Service direct URL", "enabled": true}, {"key": "crop_service_url", "value": "http://localhost:3004/api/crops", "description": "Crop Service direct URL", "enabled": true}, {"key": "farm_service_url", "value": "http://localhost:3005/api/farms", "description": "Farm Service direct URL", "enabled": true}, {"key": "notification_service_url", "value": "http://localhost:3008/api/notifications", "description": "Notification Service direct URL", "enabled": true}, {"key": "order_service_url", "value": "http://localhost:3009/api/orders", "description": "Order Service direct URL", "enabled": true}, {"key": "jwt_token", "value": "", "description": "JWT authentication token for regular users", "enabled": true}, {"key": "admin_token", "value": "", "description": "JWT authentication token for admin users", "enabled": true}, {"key": "refresh_token", "value": "", "description": "Refresh token for token renewal", "enabled": true}, {"key": "seller_id", "value": "", "description": "Current seller/farmer ID", "enabled": true}, {"key": "admin_id", "value": "", "description": "Current admin user ID", "enabled": true}, {"key": "farm_id", "value": "", "description": "Current farm ID", "enabled": true}, {"key": "crop_id", "value": "", "description": "Current crop ID", "enabled": true}, {"key": "order_id", "value": "", "description": "Current order ID", "enabled": true}, {"key": "notification_id", "value": "", "description": "Current notification ID", "enabled": true}, {"key": "template_id", "value": "", "description": "Current notification template ID", "enabled": true}, {"key": "report_id", "value": "", "description": "Current analytics report ID", "enabled": true}, {"key": "payment_id", "value": "", "description": "Current payment ID", "enabled": true}, {"key": "buyer_id", "value": "buyer-123", "description": "Sample buyer ID for testing", "enabled": true}, {"key": "listing_id", "value": "", "description": "Current marketplace listing ID", "enabled": true}, {"key": "schedule_id", "value": "", "description": "Current scheduled notification ID", "enabled": true}, {"key": "export_id", "value": "", "description": "Current data export ID", "enabled": true}, {"key": "test_email_seller", "value": "<EMAIL>", "description": "Test seller email for login", "enabled": true}, {"key": "test_password_seller", "value": "farmer123", "description": "Test seller password for login", "enabled": true}, {"key": "test_email_admin", "value": "<EMAIL>", "description": "Test admin email for login", "enabled": true}, {"key": "test_password_admin", "value": "superadmin123", "description": "Test admin password for login", "enabled": true}, {"key": "test_location_lat", "value": "12.9716", "description": "Test latitude (Bangalore)", "enabled": true}, {"key": "test_location_lng", "value": "77.5946", "description": "Test longitude (Bangalore)", "enabled": true}, {"key": "test_state", "value": "Karnataka", "description": "Test state for location-based queries", "enabled": true}, {"key": "test_district", "value": "Bangalore", "description": "Test district for location-based queries", "enabled": true}, {"key": "current_year", "value": "2024", "description": "Current year for date-based queries", "enabled": true}, {"key": "api_version", "value": "v1", "description": "API version", "enabled": true}, {"key": "content_type", "value": "application/json", "description": "Default content type for requests", "enabled": true}, {"key": "timeout", "value": "30000", "description": "Request timeout in milliseconds", "enabled": true}, {"key": "page_size", "value": "10", "description": "Default page size for paginated requests", "enabled": true}, {"key": "search_radius", "value": "50", "description": "Default search radius in kilometers", "enabled": true}], "_postman_variable_scope": "environment"}