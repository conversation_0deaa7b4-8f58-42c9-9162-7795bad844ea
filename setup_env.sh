#!/bin/bash

# Array of active services and their respective ports
declare -A services=(
  ["seller-service"]=3001
  ["admin-service"]=3002
  ["analytics-service"]=3003
  ["crop-service"]=3004
  ["farm-service"]=3005
  ["notification-service"]=3008
  ["order-service"]=3009
)

# Create .env files for each service
for service in "${!services[@]}"; do
  port=${services[$service]}
  
  # Create .env file
  cat > "apps/$service/.env" << EOF
PORT=$port
NODE_ENV=development
SERVICE_NAME=$service
EOF
  
  echo "Created .env file for $service with PORT=$port"
done

echo "Environment setup complete!" 