# Login Credentials for AgriTech Platform

This document contains the login credentials for testing the AgriTech platform after running the data seeding process.

## 🔐 Admin Login Credentials

### Super Admin
- **Email**: `<EMAIL>`
- **Password**: `superadmin123`
- **Role**: SUPER_ADMIN
- **Permissions**: All system permissions
- **Access**: Complete system administration

### Admin User
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Role**: ADMIN
- **Permissions**: Farm, crop, order management, reports
- **Access**: Standard administrative functions

### Support Staff
- **Email**: `<EMAIL>`
- **Password**: `support123`
- **Role**: SUPPORT
- **Permissions**: Read-only access to farms, crops, orders, users
- **Access**: Customer support functions

## 👨‍🌾 Seller/Farmer Login Credentials

All farmers use the same password for testing purposes: `farmer123`

### Farmer 1 - <PERSON><PERSON><PERSON> (Andhra Pradesh)
- **Email**: `<EMAIL>`
- **Password**: `farmer123`
- **Location**: Narsapur, Andhra Pradesh
- **Specialization**: Integrated farming, Rice cultivation
- **Status**: ACTIVE, VERIFIED

### Farmer 2 - Govind Patel (Gujarat)
- **Email**: `<EMAIL>`
- **Password**: `farmer123`
- **Location**: Ahmedabad, Gujarat
- **Specialization**: Cotton and oilseed farming
- **Status**: ACTIVE, VERIFIED

### Farmer 3 - Rajesh Kumar (Punjab)
- **Email**: `<EMAIL>`
- **Password**: `farmer123`
- **Location**: Ludhiana, Punjab
- **Specialization**: Wheat cultivation
- **Status**: ACTIVE, VERIFIED

### Farmer 4 - Priya Sharma (Rajasthan)
- **Email**: `<EMAIL>`
- **Password**: `farmer123`
- **Location**: Jaipur, Rajasthan
- **Specialization**: Organic vegetables
- **Status**: ACTIVE, VERIFIED

### Farmer 5 - Amit Singh (Uttar Pradesh)
- **Email**: `<EMAIL>`
- **Password**: `farmer123`
- **Location**: Meerut, Uttar Pradesh
- **Specialization**: Sugar cane farming
- **Status**: ACTIVE, VERIFIED

### Farmer 6 - Sunita Devi (Bihar)
- **Email**: `<EMAIL>`
- **Password**: `farmer123`
- **Location**: Patna, Bihar
- **Specialization**: Organic rice cultivation
- **Status**: ACTIVE, VERIFIED

## 🔧 API Endpoints for Authentication

### Admin Login
```bash
POST http://localhost:3002/api/admin/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "superadmin123"
}
```

### Seller/Farmer Login
```bash
POST http://localhost:3001/api/v1/sellers/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "farmer123"
}
```

### Via API Gateway
```bash
POST http://localhost:3000/api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "superadmin123"
}
```

## 🛡️ Security Features

### Password Encryption
- All passwords are encrypted using **bcrypt** with salt rounds of 10
- Passwords are never stored in plain text
- Password field is excluded from queries by default (`select: false`)

### JWT Authentication
- JWT tokens are issued upon successful login
- Tokens expire in 24 hours for sellers, 8 hours for admins
- Tokens include user role and permissions for authorization

### Rate Limiting
- Authentication endpoints have strict rate limiting:
  - Admin login: 5 requests per 15 minutes
  - Seller login: 10 requests per minute
  - Registration: 5 requests per minute

## 🧪 Testing Authentication

### Using cURL
```bash
# Test admin login
curl -X POST http://localhost:3002/api/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"superadmin123"}'

# Test seller login
curl -X POST http://localhost:3001/api/v1/sellers/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"farmer123"}'
```

### Using Postman
1. Import the Postman collections from `apidocs/postman/`
2. Set up environment variables with the base URLs
3. Use the authentication requests with the credentials above
4. Copy the returned JWT token to the environment variable
5. Use the token for subsequent authenticated requests

## 🔄 Data Seeding

To ensure all credentials are properly set up, run the data seeding process:

```bash
# Seed all data including users with encrypted passwords
npm run seed

# Or seed specific data types
npm run seed:admins
npm run seed:farmers
```

## 🚨 Important Notes

### For Development
- These credentials are for **development and testing only**
- Never use these credentials in production
- Change all default passwords before deploying to production

### For Production
- Generate strong, unique passwords for all accounts
- Use environment variables for sensitive configuration
- Implement proper password policies
- Enable two-factor authentication where possible
- Regular security audits and password rotation

### Password Requirements (Production)
- Minimum 12 characters
- Mix of uppercase, lowercase, numbers, and special characters
- No common dictionary words
- Regular password rotation (90 days)
- No password reuse for last 12 passwords

## 📞 Support

If you encounter authentication issues:

1. **Check Service Status**: Ensure all services are running
2. **Verify Database**: Confirm MongoDB connection and data seeding
3. **Check Logs**: Review service logs for authentication errors
4. **Test Endpoints**: Use the provided cURL commands to test
5. **Contact Support**: Reach out to the development team

### Common Issues
- **"Password not set"**: Run data seeding to ensure passwords are properly set
- **"Invalid credentials"**: Verify email and password are correct
- **"Account not active"**: Check user status in database
- **"Token expired"**: Login again to get a new JWT token

## 🔐 Password Hashes

For reference, here are the bcrypt hashes used in seeding:

- `farmer123` → `$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi`
- `admin123` → `$2b$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW`
- `superadmin123` → `$2b$10$v2zOiJQj7gyiaBle/mh.duAJkieeHxSWwjL8Tkrv41DELONvdTdO6`
- `support123` → `$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi`
