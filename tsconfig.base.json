{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "esModuleInterop": true, "paths": {"@libs/auth": ["libs/feature/auth/src/index.ts"], "@libs/shared": ["libs/shared/index.ts"]}}, "exclude": ["node_modules", "tmp"]}