#!/usr/bin/env node

/**
 * JWT Token Test Script
 * This script tests JWT token generation and verification to ensure consistency
 */

const jwt = require('jsonwebtoken');

const JWT_SECRET = 'befarma_';

console.log('🧪 JWT Token Test Script');
console.log('========================');
console.log(`🔑 JWT Secret: ${JWT_SECRET}`);
console.log('');

// Test 1: Generate a seller token (like seller service does)
console.log('📝 Test 1: Generate Seller Token');
const sellerPayload = {
  id: 'seller-123',
  email: '<EMAIL>',
  name: '<PERSON><PERSON><PERSON>',
  role: 'seller',
};

const sellerToken = jwt.sign(sellerPayload, JWT_SECRET, {
  expiresIn: '1d',
});

console.log('   ✅ Seller token generated successfully');
console.log(`   🎫 Token: ${sellerToken.substring(0, 50)}...`);
console.log('');

// Test 2: Verify the seller token (like middleware does)
console.log('🔍 Test 2: Verify Seller Token');
try {
  const decoded = jwt.verify(sellerToken, JWT_SECRET);
  console.log('   ✅ Token verified successfully');
  console.log('   📋 Decoded payload:', JSON.stringify(decoded, null, 2));
  console.log('');
} catch (error) {
  console.log('   ❌ Token verification failed');
  console.log('   🚨 Error:', error.message);
  console.log('');
}

// Test 3: Generate an admin token (like admin service does)
console.log('📝 Test 3: Generate Admin Token');
const adminPayload = {
  adminId: 'admin-123',
  role: 'SUPER_ADMIN',
  email: '<EMAIL>',
};

const adminToken = jwt.sign(adminPayload, JWT_SECRET, {
  expiresIn: '8h',
});

console.log('   ✅ Admin token generated successfully');
console.log(`   🎫 Token: ${adminToken.substring(0, 50)}...`);
console.log('');

// Test 4: Verify the admin token
console.log('🔍 Test 4: Verify Admin Token');
try {
  const decoded = jwt.verify(adminToken, JWT_SECRET);
  console.log('   ✅ Token verified successfully');
  console.log('   📋 Decoded payload:', JSON.stringify(decoded, null, 2));
  console.log('');
} catch (error) {
  console.log('   ❌ Token verification failed');
  console.log('   🚨 Error:', error.message);
  console.log('');
}

// Test 5: Test with wrong secret
console.log('🔍 Test 5: Test with Wrong Secret');
try {
  const decoded = jwt.verify(sellerToken, 'wrong_secret');
  console.log('   ❌ This should not succeed!');
} catch (error) {
  console.log('   ✅ Correctly rejected token with wrong secret');
  console.log(`   📝 Error: ${error.message}`);
  console.log('');
}

// Test 6: Test expired token
console.log('🔍 Test 6: Test Expired Token');
const expiredToken = jwt.sign(sellerPayload, JWT_SECRET, {
  expiresIn: '-1s', // Already expired
});

try {
  const decoded = jwt.verify(expiredToken, JWT_SECRET);
  console.log('   ❌ This should not succeed!');
} catch (error) {
  console.log('   ✅ Correctly rejected expired token');
  console.log(`   📝 Error: ${error.message}`);
  console.log('');
}

console.log('🎉 JWT Token Tests Completed!');
console.log('');
console.log('📋 Summary:');
console.log('   ✅ Token generation works correctly');
console.log('   ✅ Token verification works correctly');
console.log('   ✅ Wrong secret is rejected');
console.log('   ✅ Expired tokens are rejected');
console.log('');
console.log('🚀 Your JWT configuration is working properly!');
console.log('   Now restart your services and test with Postman.');
