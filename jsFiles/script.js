const bcrypt = require('bcrypt');

/**
 * Hash a plain text password using bcrypt
 * @param {string} plainPassword - The password to hash
 * @param {number} saltRounds - The cost factor (default: 10)
 * @returns {Promise<string>} - The hashed password
 */
async function hashPassword(plainPassword, saltRounds = 10) {
  const hash = await bcrypt.hash(plainPassword, saltRounds);
  return hash;
}

/**
 * Verify a password against a hash
 * @param {string} plainPassword - The plain text password
 * @param {string} hash - The bcrypt hash
 * @returns {Promise<boolean>} - True if password matches
 */
async function verifyPassword(plainPassword, hash) {
  return await bcrypt.compare(plainPassword, hash);
}

/**
 * Generate hashes for all default passwords
 */
async function generateAllHashes() {
  const passwords = {
    'superadmin123': '',
    'admin123': '',
    'support123': '',
    'farmer123': ''
  };

  console.log('🔐 Generating bcrypt hashes for AgriTech platform...\n');

  for (const [password, _] of Object.entries(passwords)) {
    const hash = await hashPassword(password);
    passwords[password] = hash;
    console.log(`${password} → ${hash}`);
  }

  console.log('\n✅ All password hashes generated successfully!');
  console.log('\n📋 Copy these hashes to your seed files:');
  console.log('-------------------------------------------');

  Object.entries(passwords).forEach(([password, hash]) => {
    console.log(`// Password: ${password}`);
    console.log(`password: '${hash}',\n`);
  });

  return passwords;
}

/**
 * Test password verification
 */
async function testPasswords() {
  console.log('\n🧪 Testing password verification...\n');

  const testCases = [
    {
      password: 'farmer123',
      hash: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
    },
    {
      password: 'admin123',
      hash: '$2b$10$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW'
    },
    {
      password: 'superadmin123',
      hash: '$2b$10$v2zOiJQj7gyiaBle/mh.duAJkieeHxSWwjL8Tkrv41DELONvdTdO6'
    }
  ];

  for (const testCase of testCases) {
    const isValid = await verifyPassword(testCase.password, testCase.hash);
    console.log(`${testCase.password}: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
  }
}

// Main execution
(async () => {
  try {
    // Check if command line arguments are provided
    const args = process.argv.slice(2);

    if (args.length > 0) {
      // Hash specific password from command line
      const password = args[0];
      const hash = await hashPassword(password);
      console.log(`Password: ${password}`);
      console.log(`Hash: ${hash}`);
    } else {
      // Generate all default hashes and test them
      await generateAllHashes();
      await testPasswords();
    }
  } catch (error) {
    console.error('Error:', error);
  }
})();