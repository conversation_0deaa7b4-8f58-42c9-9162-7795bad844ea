#!/bin/bash

# Fix JWT Secrets Across All Services
# This script ensures all services use the same JWT_SECRET

echo "🔧 Fixing JWT Secrets across all AgriTech services..."

JWT_SECRET="befarma_"
JWT_EXPIRES_IN="24h"

# Function to add JWT config to env file
add_jwt_config() {
    local env_file=$1
    local service_name=$2
    
    echo "📝 Updating $service_name environment file: $env_file"
    
    # Check if JWT_SECRET already exists
    if grep -q "JWT_SECRET" "$env_file"; then
        echo "   ✅ JWT_SECRET already exists in $env_file"
        # Update existing JWT_SECRET
        sed -i "s/^JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/" "$env_file"
        sed -i "s/^JWT_EXPIRES_IN=.*/JWT_EXPIRES_IN=$JWT_EXPIRES_IN/" "$env_file"
    else
        echo "   ➕ Adding JWT_SECRET to $env_file"
        # Add JWT configuration
        echo "" >> "$env_file"
        echo "# JWT Configuration" >> "$env_file"
        echo "JWT_SECRET=$JWT_SECRET" >> "$env_file"
        echo "JWT_EXPIRES_IN=$JWT_EXPIRES_IN" >> "$env_file"
    fi
}

# Update all service environment files
services=(
    "apps/farmer-service/.env.development:Farmer Service"
    "apps/farmer-service/.env.production:Farmer Service"
    "apps/admin-service/.env.development:Admin Service"
    "apps/admin-service/.env.production:Admin Service"
    "apps/analytics-service/.env.development:Analytics Service"
    "apps/analytics-service/.env.production:Analytics Service"
    "apps/crop-service/.env.development:Crop Service"
    "apps/crop-service/.env.production:Crop Service"
    "apps/farm-service/.env.development:Farm Service"
    "apps/farm-service/.env.production:Farm Service"
    "apps/notification-service/.env.development:Notification Service"
    "apps/notification-service/.env.production:Notification Service"
    "apps/order-service/.env.development:Order Service"
    "apps/order-service/.env.production:Order Service"
)

for service_info in "${services[@]}"; do
    IFS=':' read -r env_file service_name <<< "$service_info"
    
    # Create the file if it doesn't exist
    if [ ! -f "$env_file" ]; then
        echo "   📄 Creating $env_file"
        mkdir -p "$(dirname "$env_file")"
        
        # Extract service directory name for port assignment
        service_dir=$(echo "$env_file" | cut -d'/' -f2)
        
        # Assign ports based on service
        case $service_dir in
            "seller-service") port=3001 ;;
            "admin-service") port=3002 ;;
            "analytics-service") port=3003 ;;
            "crop-service") port=3004 ;;
            "farm-service") port=3005 ;;
            "notification-service") port=3008 ;;
            "order-service") port=3009 ;;
            *) port=3001 ;;
        esac
        
        # Determine environment
        if [[ "$env_file" == *"production"* ]]; then
            node_env="production"
        else
            node_env="development"
        fi
        
        # Create basic env file
        cat > "$env_file" << EOF
PORT=$port
NODE_ENV=$node_env
SERVICE_NAME=$service_dir
MONGODB_URI=mongodb+srv://agritech:<EMAIL>/agritech

# JWT Configuration
JWT_SECRET=$JWT_SECRET
JWT_EXPIRES_IN=$JWT_EXPIRES_IN
EOF
    else
        add_jwt_config "$env_file" "$service_name"
    fi
done

echo ""
echo "🎉 JWT Secret configuration completed!"
echo ""
echo "📋 Summary:"
echo "   🔑 JWT_SECRET: $JWT_SECRET"
echo "   ⏰ JWT_EXPIRES_IN: $JWT_EXPIRES_IN"
echo ""
echo "🚀 Next steps:"
echo "   1. Restart all services to pick up new environment variables"
echo "   2. Test authentication with Postman collections"
echo "   3. Check service logs for JWT verification success"
echo ""
echo "💡 To restart services:"
echo "   npm run serve:all"
echo ""
